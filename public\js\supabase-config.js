// Supabase Configuration
import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';

// Supabase project configuration
// يجب استبدال هذه القيم بقيم مشروعك الفعلي من Supabase Dashboard
const supabaseUrl = 'https://your-project-ref.supabase.co';
const supabaseAnonKey = 'your-anon-key-here';

// Initialize Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Gemini AI Configuration
export const GEMINI_API_KEY = 'AIzaSyCYsL7v_v0OmtxEckHYQ1j-g2J1eQKv6H8';

// App Configuration
export const APP_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedFileTypes: ['application/pdf'],
  supportedLanguages: ['ar', 'en'],
  questionTypes: ['mcq', 'trueFalse', 'fillBlank'],
  difficultyLevels: ['easy', 'medium', 'hard', 'mixed']
};

// Database table names
export const TABLES = {
  EXAMS: 'exams',
  USERS: 'users',
  EXAM_RESULTS: 'exam_results'
};

// Storage bucket names
export const STORAGE_BUCKETS = {
  PDF_FILES: 'pdf-files',
  EXAM_EXPORTS: 'exam-exports'
};

// Helper functions for authentication
export const authHelpers = {
  // Get current user
  getCurrentUser: async () => {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  },

  // Sign up with email and password
  signUp: async (email, password, userData = {}) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    });
    return { data, error };
  },

  // Sign in with email and password
  signIn: async (email, password) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    return { data, error };
  },

  // Sign in with Google
  signInWithGoogle: async () => {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: window.location.origin + '/dashboard.html'
      }
    });
    return { data, error };
  },

  // Sign out
  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  // Listen to auth changes
  onAuthStateChange: (callback) => {
    return supabase.auth.onAuthStateChange(callback);
  }
};

// Helper functions for database operations
export const dbHelpers = {
  // Create new exam
  createExam: async (examData) => {
    const user = await authHelpers.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from(TABLES.EXAMS)
      .insert({
        ...examData,
        user_id: user.id,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    return { data, error };
  },

  // Get user's exams
  getUserExams: async () => {
    const user = await authHelpers.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from(TABLES.EXAMS)
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    return { data, error };
  },

  // Get exam by ID
  getExamById: async (examId) => {
    const user = await authHelpers.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from(TABLES.EXAMS)
      .select('*')
      .eq('id', examId)
      .eq('user_id', user.id)
      .single();

    return { data, error };
  },

  // Update exam
  updateExam: async (examId, updates) => {
    const user = await authHelpers.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from(TABLES.EXAMS)
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', examId)
      .eq('user_id', user.id)
      .select()
      .single();

    return { data, error };
  },

  // Delete exam
  deleteExam: async (examId) => {
    const user = await authHelpers.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { error } = await supabase
      .from(TABLES.EXAMS)
      .delete()
      .eq('id', examId)
      .eq('user_id', user.id);

    return { error };
  },

  // Save exam result
  saveExamResult: async (examId, resultData) => {
    const user = await authHelpers.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from(TABLES.EXAM_RESULTS)
      .insert({
        exam_id: examId,
        user_id: user.id,
        ...resultData,
        completed_at: new Date().toISOString()
      })
      .select()
      .single();

    return { data, error };
  },

  // Get exam results
  getExamResults: async (examId) => {
    const user = await authHelpers.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from(TABLES.EXAM_RESULTS)
      .select('*')
      .eq('exam_id', examId)
      .eq('user_id', user.id)
      .order('completed_at', { ascending: false });

    return { data, error };
  }
};

// Helper functions for storage operations
export const storageHelpers = {
  // Upload PDF file
  uploadPDF: async (file, fileName) => {
    const user = await authHelpers.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const filePath = `${user.id}/${fileName}`;
    
    const { data, error } = await supabase.storage
      .from(STORAGE_BUCKETS.PDF_FILES)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    return { data, error };
  },

  // Get PDF file URL
  getPDFUrl: async (filePath) => {
    const { data } = supabase.storage
      .from(STORAGE_BUCKETS.PDF_FILES)
      .getPublicUrl(filePath);

    return data.publicUrl;
  },

  // Delete PDF file
  deletePDF: async (filePath) => {
    const { error } = await supabase.storage
      .from(STORAGE_BUCKETS.PDF_FILES)
      .remove([filePath]);

    return { error };
  }
};

// Utility functions
export const utils = {
  // Show notification
  showNotification: (message, type = 'info') => {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;
    
    // Set notification style based on type
    const styles = {
      success: 'bg-green-500 text-white',
      error: 'bg-red-500 text-white',
      warning: 'bg-yellow-500 text-white',
      info: 'bg-blue-500 text-white'
    };
    
    notification.className += ` ${styles[type] || styles.info}`;
    notification.innerHTML = `
      <div class="flex items-center">
        <span class="flex-1">${message}</span>
        <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200">
          <i class="fas fa-times"></i>
        </button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
      notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove();
        }
      }, 300);
    }, 5000);
  },

  // Format date
  formatDate: (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  },

  // Generate unique ID
  generateId: () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  // Validate file
  validateFile: (file) => {
    if (!file) {
      return { valid: false, error: 'لم يتم اختيار ملف' };
    }

    if (!APP_CONFIG.allowedFileTypes.includes(file.type)) {
      return { valid: false, error: 'نوع الملف غير مدعوم. يرجى اختيار ملف PDF' };
    }

    if (file.size > APP_CONFIG.maxFileSize) {
      return { valid: false, error: 'حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت' };
    }

    return { valid: true };
  }
};

// Initialize app
export const initializeApp = async () => {
  try {
    // Check if user is logged in
    const user = await authHelpers.getCurrentUser();
    
    // Update UI based on auth state
    updateAuthUI(user);
    
    // Listen for auth changes
    authHelpers.onAuthStateChange((event, session) => {
      updateAuthUI(session?.user);
    });
    
  } catch (error) {
    console.error('Error initializing app:', error);
    utils.showNotification('حدث خطأ في تهيئة التطبيق', 'error');
  }
};

// Update UI based on authentication state
const updateAuthUI = (user) => {
  const authButtons = document.querySelectorAll('.auth-required');
  const guestButtons = document.querySelectorAll('.guest-only');
  
  if (user) {
    authButtons.forEach(btn => btn.classList.remove('hidden'));
    guestButtons.forEach(btn => btn.classList.add('hidden'));
    
    // Update user info if elements exist
    const userName = document.getElementById('userName');
    const userAvatar = document.getElementById('userAvatar');
    
    if (userName) {
      userName.textContent = user.user_metadata?.name || user.email.split('@')[0];
    }
    
    if (userAvatar) {
      const name = user.user_metadata?.name || user.email;
      userAvatar.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3b82f6&color=fff`;
    }
  } else {
    authButtons.forEach(btn => btn.classList.add('hidden'));
    guestButtons.forEach(btn => btn.classList.remove('hidden'));
  }
};
