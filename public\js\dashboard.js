// Dashboard JavaScript for PDF to Exam Converter
import { 
    supabase, 
    authHelpers, 
    dbHelpers, 
    utils 
} from './supabase-config.js';

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

async function initializeDashboard() {
    // Check authentication
    const user = await checkAuth();
    if (!user) return;
    
    // Setup UI
    setupUserInterface(user);
    setupEventListeners();
    
    // Load dashboard data
    await loadDashboardData();
}

// Check authentication
async function checkAuth() {
    try {
        const user = await authHelpers.getCurrentUser();
        
        if (!user) {
            // User not logged in, redirect to auth page
            window.location.href = 'auth.html';
            return null;
        }
        
        return user;
    } catch (error) {
        console.error('Error checking auth:', error);
        window.location.href = 'auth.html';
        return null;
    }
}

// Setup user interface
function setupUserInterface(user) {
    // Update user info
    const userName = document.getElementById('userName');
    const userAvatar = document.getElementById('userAvatar');
    
    if (userName) {
        userName.textContent = user.user_metadata?.full_name || user.email.split('@')[0];
    }
    
    if (userAvatar) {
        const name = user.user_metadata?.full_name || user.email;
        userAvatar.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3b82f6&color=fff`;
    }
    
    // Setup user menu dropdown
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userDropdown = document.getElementById('userDropdown');
    
    if (userMenuBtn && userDropdown) {
        userMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            userDropdown.classList.toggle('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
            userDropdown.classList.add('hidden');
        });
    }
}

// Setup event listeners
function setupEventListeners() {
    // Logout button
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
    
    // New exam button
    const newExamBtn = document.getElementById('newExamBtn');
    if (newExamBtn) {
        newExamBtn.addEventListener('click', function() {
            window.location.href = 'upload.html';
        });
    }
    
    // Quick action buttons
    const uploadPDFBtn = document.getElementById('uploadPDFBtn');
    if (uploadPDFBtn) {
        uploadPDFBtn.addEventListener('click', function() {
            window.location.href = 'upload.html';
        });
    }
    
    const browseTemplatesBtn = document.getElementById('browseTemplatesBtn');
    if (browseTemplatesBtn) {
        browseTemplatesBtn.addEventListener('click', function() {
            utils.showNotification('قريباً: تصفح القوالب', 'info');
        });
    }
    
    const viewAnalyticsBtn = document.getElementById('viewAnalyticsBtn');
    if (viewAnalyticsBtn) {
        viewAnalyticsBtn.addEventListener('click', function() {
            utils.showNotification('قريباً: التحليلات المتقدمة', 'info');
        });
    }
    
    // View all buttons
    const viewAllExamsBtn = document.getElementById('viewAllExamsBtn');
    if (viewAllExamsBtn) {
        viewAllExamsBtn.addEventListener('click', function() {
            utils.showNotification('قريباً: صفحة جميع الامتحانات', 'info');
        });
    }
    
    const viewAllResultsBtn = document.getElementById('viewAllResultsBtn');
    if (viewAllResultsBtn) {
        viewAllResultsBtn.addEventListener('click', function() {
            utils.showNotification('قريباً: صفحة جميع النتائج', 'info');
        });
    }
}

// Handle logout
async function handleLogout() {
    try {
        showLoading(true);
        
        const { error } = await authHelpers.signOut();
        
        if (error) {
            throw error;
        }
        
        utils.showNotification('تم تسجيل الخروج بنجاح', 'success');
        
        // Redirect to home page
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1000);
        
    } catch (error) {
        console.error('Logout error:', error);
        utils.showNotification('حدث خطأ في تسجيل الخروج', 'error');
    } finally {
        showLoading(false);
    }
}

// Load dashboard data
async function loadDashboardData() {
    try {
        showLoading(true);
        
        // Get user's exams
        const { data: exams, error: examsError } = await dbHelpers.getUserExams();
        
        if (examsError) {
            throw examsError;
        }
        
        // Update stats
        updateStats(exams || []);
        
        // Load recent exams
        loadRecentExams(exams || []);
        
        // Load recent results (placeholder for now)
        loadRecentResults([]);
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        utils.showNotification('حدث خطأ في تحميل البيانات', 'error');
    } finally {
        showLoading(false);
    }
}

// Update statistics
function updateStats(exams) {
    const totalExams = exams.length;
    const totalAttempts = 0; // Will be calculated from exam_results table
    const averageScore = 0; // Will be calculated from exam_results table
    const lastActivity = exams.length > 0 ? exams[0].created_at : null;
    
    // Update DOM elements
    const totalExamsEl = document.getElementById('totalExams');
    const totalAttemptsEl = document.getElementById('totalAttempts');
    const averageScoreEl = document.getElementById('averageScore');
    const lastActivityEl = document.getElementById('lastActivity');
    
    if (totalExamsEl) totalExamsEl.textContent = totalExams;
    if (totalAttemptsEl) totalAttemptsEl.textContent = totalAttempts;
    if (averageScoreEl) averageScoreEl.textContent = `${averageScore}%`;
    if (lastActivityEl) {
        lastActivityEl.textContent = lastActivity ? 
            utils.formatDate(lastActivity) : 'لا يوجد نشاط';
    }
}

// Load recent exams
function loadRecentExams(exams) {
    const container = document.getElementById('recentExamsList');
    if (!container) return;
    
    if (exams.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-file-alt text-4xl mb-4"></i>
                <p>لا توجد امتحانات بعد</p>
                <button class="btn-primary mt-4" onclick="window.location.href='upload.html'">
                    إنشاء أول امتحان
                </button>
            </div>
        `;
        return;
    }
    
    const recentExams = exams.slice(0, 5); // Show only 5 recent exams
    
    container.innerHTML = recentExams.map(exam => `
        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-file-alt text-primary-600"></i>
                </div>
                <div>
                    <h3 class="font-medium text-gray-900">${exam.title}</h3>
                    <p class="text-sm text-gray-500">${utils.formatDate(exam.created_at)}</p>
                </div>
            </div>
            <div class="flex items-center space-x-2 space-x-reverse">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(exam.status)}">
                    ${getStatusText(exam.status)}
                </span>
                <button onclick="viewExam('${exam.id}')" class="text-primary-600 hover:text-primary-700">
                    <i class="fas fa-eye"></i>
                </button>
                <button onclick="editExam('${exam.id}')" class="text-gray-600 hover:text-gray-700">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// Load recent results
function loadRecentResults(results) {
    const container = document.getElementById('recentResultsList');
    if (!container) return;
    
    if (results.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-chart-line text-4xl mb-4"></i>
                <p>لا توجد نتائج بعد</p>
            </div>
        `;
        return;
    }
    
    // Implementation for when we have results data
}

// Helper functions
function getStatusBadgeClass(status) {
    switch (status) {
        case 'published':
            return 'bg-green-100 text-green-800';
        case 'draft':
            return 'bg-yellow-100 text-yellow-800';
        case 'archived':
            return 'bg-gray-100 text-gray-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'published':
            return 'منشور';
        case 'draft':
            return 'مسودة';
        case 'archived':
            return 'مؤرشف';
        default:
            return 'غير محدد';
    }
}

// Global functions for exam actions
window.viewExam = function(examId) {
    window.location.href = `exam-preview.html?id=${examId}`;
};

window.editExam = function(examId) {
    window.location.href = `exam-preview.html?id=${examId}&edit=true`;
};

// Show/hide loading overlay
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.toggle('hidden', !show);
    }
}
