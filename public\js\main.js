// Main JavaScript file for PDF to Exam Converter
import {
    supabase,
    authHelpers,
    dbHelpers,
    storageHelpers,
    utils,
    initializeApp as initSupabase
} from './supabase-config.js';

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    initializeApp();
});

async function initializeApp() {
    // Initialize Supabase
    await initSupabase();

    // Mobile menu functionality
    setupMobileMenu();

    // Smooth scrolling for navigation links
    setupSmoothScrolling();

    // Button event listeners
    setupButtonListeners();

    // Contact form
    setupContactForm();

    // Check authentication state
    checkAuthState();
}

// Mobile menu setup
function setupMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
}

// Smooth scrolling for navigation links
function setupSmoothScrolling() {
    const navLinks = document.querySelectorAll('a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Close mobile menu if open
                const mobileMenu = document.getElementById('mobileMenu');
                if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                }
            }
        });
    });
}

// Setup button event listeners
function setupButtonListeners() {
    // Start Now buttons
    const startButtons = ['startNowBtn', 'tryNowBtn', 'getStartedBtn'];
    startButtons.forEach(btnId => {
        const btn = document.getElementById(btnId);
        if (btn) {
            btn.addEventListener('click', function() {
                // Check if user is authenticated
                if (isUserAuthenticated()) {
                    window.location.href = 'upload.html';
                } else {
                    showAuthModal('signup');
                }
            });
        }
    });
    
    // Watch demo button
    const watchDemoBtn = document.getElementById('watchDemoBtn');
    if (watchDemoBtn) {
        watchDemoBtn.addEventListener('click', function() {
            // Show demo modal or redirect to demo video
            showDemoModal();
        });
    }
    
    // Auth buttons
    const loginBtn = document.getElementById('loginBtn');
    const signupBtn = document.getElementById('signupBtn');
    const mobileLoginBtn = document.getElementById('mobileLoginBtn');
    const mobileSignupBtn = document.getElementById('mobileSignupBtn');
    
    if (loginBtn) {
        loginBtn.addEventListener('click', () => showAuthModal('login'));
    }
    if (signupBtn) {
        signupBtn.addEventListener('click', () => showAuthModal('signup'));
    }
    if (mobileLoginBtn) {
        mobileLoginBtn.addEventListener('click', () => showAuthModal('login'));
    }
    if (mobileSignupBtn) {
        mobileSignupBtn.addEventListener('click', () => showAuthModal('signup'));
    }
}

// Contact form setup
function setupContactForm() {
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                subject: formData.get('subject'),
                message: formData.get('message')
            };
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري الإرسال...';
            submitBtn.disabled = true;
            
            // Simulate form submission (replace with actual implementation)
            setTimeout(() => {
                showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.', 'success');
                contactForm.reset();
                
                // Restore button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
    }
}

// Check authentication state
async function checkAuthState() {
    try {
        const user = await authHelpers.getCurrentUser();

        if (user) {
            updateUIForAuthenticatedUser(user);
        } else {
            updateUIForLoggedOutUser();
        }
    } catch (error) {
        console.error('Error checking auth state:', error);
        updateUIForLoggedOutUser();
    }
}

// Update UI for authenticated user
function updateUIForAuthenticatedUser(user) {
    const loginBtn = document.getElementById('loginBtn');
    const signupBtn = document.getElementById('signupBtn');
    const mobileLoginBtn = document.getElementById('mobileLoginBtn');
    const mobileSignupBtn = document.getElementById('mobileSignupBtn');
    
    if (loginBtn) {
        loginBtn.innerHTML = `<i class="fas fa-user mr-2"></i>${user.name || user.email}`;
        loginBtn.onclick = () => window.location.href = 'dashboard.html';
    }
    
    if (signupBtn) {
        signupBtn.innerHTML = '<i class="fas fa-sign-out-alt mr-2"></i>تسجيل خروج';
        signupBtn.onclick = logout;
    }
    
    if (mobileLoginBtn) {
        mobileLoginBtn.innerHTML = `<i class="fas fa-user mr-2"></i>${user.name || user.email}`;
        mobileLoginBtn.onclick = () => window.location.href = 'dashboard.html';
    }
    
    if (mobileSignupBtn) {
        mobileSignupBtn.innerHTML = '<i class="fas fa-sign-out-alt mr-2"></i>تسجيل خروج';
        mobileSignupBtn.onclick = logout;
    }
}

// Check if user is authenticated
function isUserAuthenticated() {
    return localStorage.getItem('user') !== null;
}

// Show authentication modal
function showAuthModal(type) {
    // Redirect to auth page for now
    window.location.href = `auth.html?type=${type}`;
}

// Show demo modal
function showDemoModal() {
    showNotification('العرض التوضيحي قريباً!', 'info');
}

// Logout function
function logout() {
    localStorage.removeItem('user');
    showNotification('تم تسجيل الخروج بنجاح', 'success');
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Utility function to show notifications
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;
    
    // Set notification style based on type
    switch (type) {
        case 'success':
            notification.classList.add('bg-green-500', 'text-white');
            break;
        case 'error':
            notification.classList.add('bg-red-500', 'text-white');
            break;
        case 'warning':
            notification.classList.add('bg-yellow-500', 'text-white');
            break;
        default:
            notification.classList.add('bg-blue-500', 'text-white');
    }
    
    notification.innerHTML = `
        <div class="flex items-center">
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// Utility function to format dates
function formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

// Update UI for logged out user
function updateUIForLoggedOutUser() {
    const loginBtn = document.getElementById('loginBtn');
    const signupBtn = document.getElementById('signupBtn');
    const mobileLoginBtn = document.getElementById('mobileLoginBtn');
    const userMenu = document.getElementById('userMenu');

    if (loginBtn) loginBtn.style.display = 'inline-block';
    if (signupBtn) signupBtn.style.display = 'inline-block';
    if (mobileLoginBtn) mobileLoginBtn.style.display = 'block';
    if (userMenu) userMenu.style.display = 'none';
}

// Utility function to validate email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Export functions for use in other files
window.showNotification = showNotification;
window.formatDate = formatDate;
window.isValidEmail = isValidEmail;
window.isUserAuthenticated = isUserAuthenticated;
