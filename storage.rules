rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Users can only upload/access their own PDF files
    match /pdfs/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public assets (logos, images, etc.)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
