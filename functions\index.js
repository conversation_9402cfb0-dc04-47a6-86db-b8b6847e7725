const functions = require('firebase-functions');
const admin = require('firebase-admin');
const cors = require('cors')({ origin: true });
const axios = require('axios');
const pdfParse = require('pdf-parse');

// Initialize Firebase Admin
admin.initializeApp();

// Gemini API Configuration
const GEMINI_API_KEY = 'AIzaSyCYsL7v_v0OmtxEckHYQ1j-g2J1eQKv6H8';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

/**
 * Cloud Function to analyze PDF and generate exam questions
 */
exports.generateExamFromPDF = functions.https.onCall(async (data, context) => {
  try {
    // Check authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'المستخدم غير مصادق عليه');
    }

    const { fileUrl, config } = data;

    if (!fileUrl) {
      throw new functions.https.HttpsError('invalid-argument', 'رابط الملف مطلوب');
    }

    // Step 1: Download and parse PDF
    console.log('Downloading PDF from:', fileUrl);
    const pdfText = await extractTextFromPDF(fileUrl);

    if (!pdfText || pdfText.trim().length === 0) {
      throw new functions.https.HttpsError('invalid-argument', 'لا يمكن استخراج النص من ملف PDF');
    }

    // Step 2: Generate questions using Gemini AI
    console.log('Generating questions with Gemini AI...');
    const questions = await generateQuestionsWithGemini(pdfText, config);

    // Step 3: Save exam to Firestore
    const examData = {
      userId: context.auth.uid,
      title: config.title || 'امتحان من PDF',
      description: config.description || 'امتحان تم إنشاؤه تلقائياً',
      questions: questions,
      config: config,
      fileUrl: fileUrl,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    const examRef = await admin.firestore().collection('exams').add(examData);

    return {
      success: true,
      examId: examRef.id,
      questionsCount: questions.length
    };

  } catch (error) {
    console.error('Error generating exam:', error);
    throw new functions.https.HttpsError('internal', error.message);
  }
});

/**
 * Extract text from PDF file
 */
async function extractTextFromPDF(fileUrl) {
  try {
    // Download PDF file
    const response = await axios.get(fileUrl, {
      responseType: 'arraybuffer',
      timeout: 30000
    });

    // Parse PDF
    const pdfBuffer = Buffer.from(response.data);
    const pdfData = await pdfParse(pdfBuffer);

    return pdfData.text;
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    throw new Error('فشل في استخراج النص من ملف PDF');
  }
}

/**
 * Generate questions using Gemini AI
 */
async function generateQuestionsWithGemini(text, config) {
  try {
    const prompt = createPromptForGemini(text, config);

    const response = await axios.post(
      `${GEMINI_API_URL}?key=${GEMINI_API_KEY}`,
      {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        }
      },
      {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      }
    );

    if (!response.data.candidates || response.data.candidates.length === 0) {
      throw new Error('لم يتم الحصول على استجابة من الذكاء الاصطناعي');
    }

    const generatedText = response.data.candidates[0].content.parts[0].text;
    const questions = parseQuestionsFromResponse(generatedText, config);

    return questions;
  } catch (error) {
    console.error('Error generating questions with Gemini:', error);
    throw new Error('فشل في توليد الأسئلة باستخدام الذكاء الاصطناعي');
  }
}

/**
 * Create prompt for Gemini AI
 */
function createPromptForGemini(text, config) {
  const language = config.language === 'ar' ? 'العربية' : 'الإنجليزية';
  const questionTypes = [];
  
  if (config.questionTypes.mcq) questionTypes.push('اختيار من متعدد');
  if (config.questionTypes.trueFalse) questionTypes.push('صح وخطأ');
  if (config.questionTypes.fillBlank) questionTypes.push('املأ الفراغ');

  let prompt = `
أنت مساعد ذكي متخصص في إنشاء الامتحانات التعليمية. مهمتك هي تحليل النص التالي وإنشاء أسئلة امتحان عالية الجودة.

النص المراد تحليله:
"""
${text.substring(0, 4000)} // تحديد النص لتجنب تجاوز حد الرموز
"""

متطلبات الامتحان:
- اللغة: ${language}
- عدد الأسئلة: ${config.questionCount}
- أنواع الأسئلة: ${questionTypes.join(', ')}
- مستوى الصعوبة: ${config.difficulty}
- تضمين شرح الإجابات: ${config.includeExplanations ? 'نعم' : 'لا'}

تعليمات مهمة:
1. اقرأ النص بعناية وافهم المحتوى الرئيسي
2. أنشئ أسئلة متنوعة تغطي النقاط المهمة في النص
3. تأكد من أن الأسئلة واضحة ومفهومة
4. اجعل الأسئلة متدرجة في الصعوبة
5. تأكد من صحة الإجابات

صيغة الإخراج المطلوبة (JSON):
{
  "questions": [
    {
      "id": 1,
      "type": "mcq",
      "question": "نص السؤال",
      "options": ["الخيار 1", "الخيار 2", "الخيار 3", "الخيار 4"],
      "correctAnswer": 0,
      "explanation": "شرح الإجابة الصحيحة",
      "difficulty": "medium"
    },
    {
      "id": 2,
      "type": "trueFalse",
      "question": "نص السؤال",
      "correctAnswer": true,
      "explanation": "شرح الإجابة",
      "difficulty": "easy"
    },
    {
      "id": 3,
      "type": "fillBlank",
      "question": "أكمل الجملة: النص مع _____ للفراغ",
      "correctAnswer": "الإجابة الصحيحة",
      "explanation": "شرح الإجابة",
      "difficulty": "hard"
    }
  ]
}

ابدأ بإنشاء الأسئلة الآن:
`;

  return prompt;
}

/**
 * Parse questions from Gemini response
 */
function parseQuestionsFromResponse(responseText, config) {
  try {
    // Try to extract JSON from the response
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('لم يتم العثور على JSON في الاستجابة');
    }

    const parsedResponse = JSON.parse(jsonMatch[0]);
    
    if (!parsedResponse.questions || !Array.isArray(parsedResponse.questions)) {
      throw new Error('تنسيق الاستجابة غير صحيح');
    }

    // Validate and clean questions
    const validQuestions = parsedResponse.questions
      .filter(q => q.question && q.question.trim().length > 0)
      .slice(0, config.questionCount)
      .map((q, index) => ({
        id: index + 1,
        type: q.type || 'mcq',
        question: q.question.trim(),
        options: q.options || [],
        correctAnswer: q.correctAnswer,
        explanation: config.includeExplanations ? q.explanation : null,
        difficulty: q.difficulty || config.difficulty
      }));

    if (validQuestions.length === 0) {
      throw new Error('لم يتم إنشاء أسئلة صالحة');
    }

    return validQuestions;
  } catch (error) {
    console.error('Error parsing questions:', error);
    // Fallback: generate sample questions
    return generateFallbackQuestions(config);
  }
}

/**
 * Generate fallback questions if AI fails
 */
function generateFallbackQuestions(config) {
  const questions = [];
  let questionId = 1;

  // Generate sample questions based on config
  for (let i = 0; i < config.questionCount; i++) {
    if (config.questionTypes.mcq && questions.length < config.questionCount) {
      questions.push({
        id: questionId++,
        type: 'mcq',
        question: `سؤال اختيار من متعدد رقم ${questions.length + 1} (تم إنشاؤه تلقائياً)`,
        options: ['الخيار الأول', 'الخيار الثاني', 'الخيار الثالث', 'الخيار الرابع'],
        correctAnswer: 0,
        explanation: config.includeExplanations ? 'شرح الإجابة الصحيحة' : null,
        difficulty: config.difficulty
      });
    }

    if (config.questionTypes.trueFalse && questions.length < config.questionCount) {
      questions.push({
        id: questionId++,
        type: 'trueFalse',
        question: `سؤال صح وخطأ رقم ${questions.length + 1} (تم إنشاؤه تلقائياً)`,
        correctAnswer: true,
        explanation: config.includeExplanations ? 'شرح الإجابة الصحيحة' : null,
        difficulty: config.difficulty
      });
    }

    if (config.questionTypes.fillBlank && questions.length < config.questionCount) {
      questions.push({
        id: questionId++,
        type: 'fillBlank',
        question: `أكمل الجملة التالية: هذا سؤال املأ الفراغ رقم ${questions.length + 1} ______ (تم إنشاؤه تلقائياً)`,
        correctAnswer: 'الإجابة الصحيحة',
        explanation: config.includeExplanations ? 'شرح الإجابة الصحيحة' : null,
        difficulty: config.difficulty
      });
    }
  }

  return questions.slice(0, config.questionCount);
}

/**
 * HTTP endpoint for testing (remove in production)
 */
exports.testGeminiAPI = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    try {
      const testText = "هذا نص تجريبي لاختبار واجهة برمجة التطبيقات";
      const testConfig = {
        questionCount: 3,
        questionTypes: { mcq: true, trueFalse: true, fillBlank: false },
        difficulty: 'medium',
        includeExplanations: true,
        language: 'ar'
      };

      const questions = await generateQuestionsWithGemini(testText, testConfig);
      
      res.json({
        success: true,
        questions: questions
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });
});
