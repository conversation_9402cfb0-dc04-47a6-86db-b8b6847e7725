{"name": "functions", "scripts": {"lint": "eslint \"src/**/*\"", "lint:fix": "eslint \"src/**/*\" --fix", "build": "tsc", "build:watch": "tsc --watch", "mocha": "mocha '**/*.spec.ts'", "test": "(cd integration-tests && firebase emulators:exec 'npm run mocha' -P demo-test)"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1"}, "devDependencies": {"@types/chai": "^4.3.4", "@types/mocha": "^10.0.1", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.15.1", "eslint-plugin-import": "^2.26.0", "eslint-config-google": "^0.14.0", "typescript": "^4.9.0", "axios": "^1.3.2", "chai": "^4.3.7", "mocha": "^10.2.0", "ts-node": "^10.4.0"}, "private": true}