-- Supabase Database Schema for PDF to Exam Converter
-- Run this SQL in Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE question_type AS ENUM ('mcq', 'trueFalse', 'fillBlank');
CREATE TYPE difficulty_level AS ENUM ('easy', 'medium', 'hard', 'mixed');
CREATE TYPE exam_status AS ENUM ('draft', 'published', 'archived');

-- Create exams table
CREATE TABLE IF NOT EXISTS exams (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    pdf_filename VARCHAR(255),
    pdf_url TEXT,
    questions JSONB NOT NULL DEFAULT '[]',
    config J<PERSON>NB NOT NULL DEFAULT '{}',
    status exam_status DEFAULT 'draft',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create exam_results table
CREATE TABLE IF NOT EXISTS exam_results (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    exam_id UUID REFERENCES exams(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    answers JSONB NOT NULL DEFAULT '{}',
    score INTEGER NOT NULL DEFAULT 0,
    total_questions INTEGER NOT NULL DEFAULT 0,
    percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    time_taken INTEGER, -- in seconds
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_profiles table (optional, for additional user data)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name VARCHAR(255),
    avatar_url TEXT,
    preferred_language VARCHAR(10) DEFAULT 'ar',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_exams_user_id ON exams(user_id);
CREATE INDEX IF NOT EXISTS idx_exams_created_at ON exams(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_exams_status ON exams(status);
CREATE INDEX IF NOT EXISTS idx_exam_results_exam_id ON exam_results(exam_id);
CREATE INDEX IF NOT EXISTS idx_exam_results_user_id ON exam_results(user_id);
CREATE INDEX IF NOT EXISTS idx_exam_results_completed_at ON exam_results(completed_at DESC);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_exams_updated_at 
    BEFORE UPDATE ON exams 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE exams ENABLE ROW LEVEL SECURITY;
ALTER TABLE exam_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Exams policies
CREATE POLICY "Users can view their own exams" ON exams
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own exams" ON exams
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own exams" ON exams
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own exams" ON exams
    FOR DELETE USING (auth.uid() = user_id);

-- Exam results policies
CREATE POLICY "Users can view their own exam results" ON exam_results
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own exam results" ON exam_results
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own exam results" ON exam_results
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own exam results" ON exam_results
    FOR DELETE USING (auth.uid() = user_id);

-- User profiles policies
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Storage policies (run these in Storage > Policies section)

-- PDF files bucket policies
-- CREATE POLICY "Users can upload their own PDF files" ON storage.objects
--     FOR INSERT WITH CHECK (bucket_id = 'pdf-files' AND auth.uid()::text = (storage.foldername(name))[1]);

-- CREATE POLICY "Users can view their own PDF files" ON storage.objects
--     FOR SELECT USING (bucket_id = 'pdf-files' AND auth.uid()::text = (storage.foldername(name))[1]);

-- CREATE POLICY "Users can delete their own PDF files" ON storage.objects
--     FOR DELETE USING (bucket_id = 'pdf-files' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Functions for common operations

-- Function to get exam statistics
CREATE OR REPLACE FUNCTION get_exam_stats(exam_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_attempts', COUNT(*),
        'average_score', ROUND(AVG(percentage), 2),
        'highest_score', MAX(percentage),
        'lowest_score', MIN(percentage),
        'average_time', ROUND(AVG(time_taken), 0)
    ) INTO result
    FROM exam_results 
    WHERE exam_id = exam_uuid;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user dashboard data
CREATE OR REPLACE FUNCTION get_user_dashboard_data(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_exams', (
            SELECT COUNT(*) FROM exams WHERE user_id = user_uuid
        ),
        'total_attempts', (
            SELECT COUNT(*) FROM exam_results WHERE user_id = user_uuid
        ),
        'average_score', (
            SELECT ROUND(AVG(percentage), 2) FROM exam_results WHERE user_id = user_uuid
        ),
        'recent_exams', (
            SELECT json_agg(
                json_build_object(
                    'id', id,
                    'title', title,
                    'created_at', created_at,
                    'status', status
                )
            )
            FROM exams 
            WHERE user_id = user_uuid 
            ORDER BY created_at DESC 
            LIMIT 5
        ),
        'recent_results', (
            SELECT json_agg(
                json_build_object(
                    'exam_id', er.exam_id,
                    'exam_title', e.title,
                    'score', er.score,
                    'percentage', er.percentage,
                    'completed_at', er.completed_at
                )
            )
            FROM exam_results er
            JOIN exams e ON er.exam_id = e.id
            WHERE er.user_id = user_uuid 
            ORDER BY er.completed_at DESC 
            LIMIT 5
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert sample data (optional, for testing)
-- INSERT INTO exams (user_id, title, description, questions, config) VALUES
-- (
--     auth.uid(),
--     'امتحان تجريبي',
--     'هذا امتحان تجريبي لاختبار النظام',
--     '[
--         {
--             "id": 1,
--             "type": "mcq",
--             "question": "ما هي عاصمة السعودية؟",
--             "options": ["الرياض", "جدة", "الدمام", "مكة"],
--             "correctAnswer": 0,
--             "explanation": "الرياض هي العاصمة الرسمية للمملكة العربية السعودية"
--         }
--     ]',
--     '{
--         "language": "ar",
--         "questionTypes": ["mcq"],
--         "difficulty": "medium",
--         "examDuration": 30
--     }'
-- );

-- Create storage buckets (run these commands in Supabase Dashboard > Storage)
-- 1. Create bucket 'pdf-files' with public access disabled
-- 2. Create bucket 'exam-exports' with public access disabled

-- Note: After running this schema, you need to:
-- 1. Create the storage buckets in Supabase Dashboard
-- 2. Set up the storage policies
-- 3. Configure authentication providers (Google OAuth)
-- 4. Update the Supabase configuration in your app
