#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load("@bazel_skylib//lib:selects.bzl", "selects")

package(default_visibility = ["//visibility:public"])

licenses(["notice"])

config_setting(
    name = "clang_compiler",
    flag_values = {
        "@bazel_tools//tools/cpp:compiler": "clang",
    },
    visibility = [":__subpackages__"],
)

config_setting(
    name = "mingw_unspecified_compiler",
    flag_values = {
        "@bazel_tools//tools/cpp:compiler": "mingw",
    },
    visibility = [":__subpackages__"],
)

config_setting(
    name = "mingw-gcc_compiler",
    flag_values = {
        "@bazel_tools//tools/cpp:compiler": "mingw-gcc",
    },
    visibility = [":__subpackages__"],
)

config_setting(
    name = "fuchsia",
    constraint_values = [
        "@platforms//os:fuchsia",
    ],
    visibility = [":__subpackages__"],
)

selects.config_setting_group(
    name = "mingw_compiler",
    match_any = [
        ":mingw_unspecified_compiler",
        ":mingw-gcc_compiler",
    ],
    visibility = [":__subpackages__"],
)
