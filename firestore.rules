rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Exams - users can only access their own exams
    match /exams/{examId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Exam results - users can only access their own results
    match /examResults/{resultId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Public exams (if shared)
    match /publicExams/{examId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == resource.data.createdBy;
    }
  }
}
