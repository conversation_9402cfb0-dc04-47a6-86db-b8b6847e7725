#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_DEFAULT_LINKOPTS",
    "ABSL_TEST_COPTS",
)

package(
    default_visibility = ["//visibility:public"],
    features = [
        "header_modules",
        "layering_check",
        "parse_headers",
    ],
)

licenses(["notice"])

cc_library(
    name = "stacktrace",
    srcs = [
        "internal/stacktrace_aarch64-inl.inc",
        "internal/stacktrace_arm-inl.inc",
        "internal/stacktrace_config.h",
        "internal/stacktrace_emscripten-inl.inc",
        "internal/stacktrace_generic-inl.inc",
        "internal/stacktrace_powerpc-inl.inc",
        "internal/stacktrace_riscv-inl.inc",
        "internal/stacktrace_unimplemented-inl.inc",
        "internal/stacktrace_win32-inl.inc",
        "internal/stacktrace_x86-inl.inc",
        "stacktrace.cc",
    ],
    hdrs = ["stacktrace.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":debugging_internal",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "//absl/base:raw_logging_internal",
    ],
)

cc_test(
    name = "stacktrace_test",
    srcs = ["stacktrace_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":stacktrace",
        "//absl/base:core_headers",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "symbolize",
    srcs = [
        "symbolize.cc",
        "symbolize_darwin.inc",
        "symbolize_elf.inc",
        "symbolize_emscripten.inc",
        "symbolize_unimplemented.inc",
        "symbolize_win32.inc",
    ],
    hdrs = [
        "internal/symbolize.h",
        "symbolize.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS + select({
        "@rules_cc//cc/compiler:msvc-cl": ["-DEFAULTLIB:dbghelp.lib"],
        "@rules_cc//cc/compiler:clang-cl": ["-DEFAULTLIB:dbghelp.lib"],
        "//absl:mingw_compiler": [
            "-DEFAULTLIB:dbghelp.lib",
            "-ldbghelp",
        ],
        "//conditions:default": [],
    }),
    deps = [
        ":debugging_internal",
        ":demangle_internal",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "//absl/base:malloc_internal",
        "//absl/base:raw_logging_internal",
        "//absl/strings",
    ],
)

cc_test(
    name = "symbolize_test",
    srcs = ["symbolize_test.cc"],
    copts = ABSL_TEST_COPTS + select({
        "@rules_cc//cc/compiler:msvc-cl": ["/Z7"],
        "@rules_cc//cc/compiler:clang-cl": ["/Z7"],
        "//conditions:default": [],
    }),
    linkopts = ABSL_DEFAULT_LINKOPTS + select({
        "@rules_cc//cc/compiler:msvc-cl": ["/DEBUG"],
        "@rules_cc//cc/compiler:clang-cl": ["/DEBUG"],
        "//conditions:default": [],
    }),
    deps = [
        ":stack_consumption",
        ":symbolize",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/log",
        "//absl/log:check",
        "//absl/memory",
        "//absl/strings",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "examine_stack",
    srcs = [
        "internal/examine_stack.cc",
    ],
    hdrs = [
        "internal/examine_stack.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = ["//absl/log/internal:__pkg__"],
    deps = [
        ":stacktrace",
        ":symbolize",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
    ],
)

cc_library(
    name = "failure_signal_handler",
    srcs = ["failure_signal_handler.cc"],
    hdrs = ["failure_signal_handler.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":examine_stack",
        ":stacktrace",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
    ],
)

cc_test(
    name = "failure_signal_handler_test",
    srcs = ["failure_signal_handler_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = select({
        "@rules_cc//cc/compiler:msvc-cl": [],
        "@rules_cc//cc/compiler:clang-cl": [],
        "@rules_cc//cc/compiler:emscripten": [],
        "//conditions:default": ["-pthread"],
    }) + ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":failure_signal_handler",
        ":stacktrace",
        ":symbolize",
        "//absl/base:raw_logging_internal",
        "//absl/log:check",
        "//absl/strings",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "debugging_internal",
    srcs = [
        "internal/address_is_readable.cc",
        "internal/elf_mem_image.cc",
        "internal/vdso_support.cc",
    ],
    hdrs = [
        "internal/address_is_readable.h",
        "internal/elf_mem_image.h",
        "internal/vdso_support.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = ["//visibility:private"],
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "//absl/base:errno_saver",
        "//absl/base:raw_logging_internal",
    ],
)

cc_library(
    name = "demangle_internal",
    srcs = ["internal/demangle.cc"],
    hdrs = ["internal/demangle.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//absl/container:__pkg__",
        "//absl/debugging:__pkg__",
    ],
    deps = [
        ":demangle_rust",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:nullability",
        "//absl/numeric:bits",
    ],
)

cc_test(
    name = "demangle_test",
    srcs = ["internal/demangle_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":demangle_internal",
        ":stack_consumption",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/log",
        "//absl/memory",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "bounded_utf8_length_sequence",
    hdrs = ["internal/bounded_utf8_length_sequence.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/numeric:bits",
    ],
)

cc_test(
    name = "bounded_utf8_length_sequence_test",
    srcs = ["internal/bounded_utf8_length_sequence_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":bounded_utf8_length_sequence",
        "//absl/base:config",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "decode_rust_punycode",
    srcs = ["internal/decode_rust_punycode.cc"],
    hdrs = ["internal/decode_rust_punycode.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":bounded_utf8_length_sequence",
        ":utf8_for_code_point",
        "//absl/base:config",
        "//absl/base:nullability",
    ],
)

cc_test(
    name = "decode_rust_punycode_test",
    srcs = ["internal/decode_rust_punycode_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":decode_rust_punycode",
        "//absl/base:config",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "demangle_rust",
    srcs = ["internal/demangle_rust.cc"],
    hdrs = ["internal/demangle_rust.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":decode_rust_punycode",
        "//absl/base:config",
        "//absl/base:core_headers",
    ],
)

cc_test(
    name = "demangle_rust_test",
    srcs = ["internal/demangle_rust_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":demangle_rust",
        "//absl/base:config",
        "//absl/base:core_headers",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "utf8_for_code_point",
    srcs = ["internal/utf8_for_code_point.cc"],
    hdrs = ["internal/utf8_for_code_point.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = ["//absl/base:config"],
)

cc_test(
    name = "utf8_for_code_point_test",
    srcs = ["internal/utf8_for_code_point_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":utf8_for_code_point",
        "//absl/base:config",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "leak_check",
    srcs = ["leak_check.cc"],
    hdrs = ["leak_check.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
    ],
)

cc_test(
    name = "leak_check_test",
    srcs = ["leak_check_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["notsan"],
    deps = [
        ":leak_check",
        "//absl/base:config",
        "//absl/log",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

# Binary that leaks memory and expects to fail on exit.  This isn't a
# test that expected to pass on its own; it exists to be called by a
# script that checks exit status and output.
# TODO(absl-team): Write a test to run this with a script that
# verifies that it correctly fails.
cc_binary(
    name = "leak_check_fail_test_binary",
    srcs = ["leak_check_fail_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":leak_check",
        "//absl/log",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "stack_consumption",
    testonly = 1,
    srcs = ["internal/stack_consumption.cc"],
    hdrs = ["internal/stack_consumption.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = ["//visibility:private"],
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
    ],
)

cc_test(
    name = "stack_consumption_test",
    srcs = ["internal/stack_consumption_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["notsan"],
    deps = [
        ":stack_consumption",
        "//absl/base:core_headers",
        "//absl/log",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_binary(
    name = "stacktrace_benchmark",
    testonly = 1,
    srcs = ["stacktrace_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    deps = [
        ":stacktrace",
        "//absl/base:config",
        "//absl/base:core_headers",
        "@google_benchmark//:benchmark_main",
    ],
)
