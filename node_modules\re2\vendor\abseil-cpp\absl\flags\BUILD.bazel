#
# Copyright 2019 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_DEFAULT_LINKOPTS",
    "ABSL_TEST_COPTS",
)

package(
    default_visibility = ["//visibility:public"],
    features = [
        "header_modules",
        "layering_check",
        "parse_headers",
    ],
)

licenses(["notice"])

cc_library(
    name = "path_util",
    hdrs = [
        "internal/path_util.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//absl/flags:__pkg__",
    ],
    deps = [
        "//absl/base:config",
        "//absl/strings",
    ],
)

cc_library(
    name = "program_name",
    srcs = [
        "internal/program_name.cc",
    ],
    hdrs = [
        "internal/program_name.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//absl/flags:__pkg__",
        "//absl/log:__pkg__",
    ],
    deps = [
        ":path_util",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:no_destructor",
        "//absl/strings",
        "//absl/synchronization",
    ],
)

cc_library(
    name = "config",
    srcs = [
        "usage_config.cc",
    ],
    hdrs = [
        "config.h",
        "usage_config.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":path_util",
        ":program_name",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:no_destructor",
        "//absl/strings",
        "//absl/synchronization",
    ],
)

cc_library(
    name = "marshalling",
    srcs = [
        "marshalling.cc",
    ],
    hdrs = [
        "marshalling.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:log_severity",
        "//absl/numeric:int128",
        "//absl/strings",
        "//absl/strings:str_format",
        "//absl/types:optional",
    ],
)

cc_library(
    name = "commandlineflag_internal",
    srcs = [
        "internal/commandlineflag.cc",
    ],
    hdrs = [
        "internal/commandlineflag.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//visibility:private",
    ],
    deps = [
        "//absl/base:config",
        "//absl/base:fast_type_id",
    ],
)

cc_library(
    name = "commandlineflag",
    srcs = [
        "commandlineflag.cc",
    ],
    hdrs = [
        "commandlineflag.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":commandlineflag_internal",
        "//absl/base:config",
        "//absl/base:fast_type_id",
        "//absl/strings",
        "//absl/types:optional",
    ],
)

cc_library(
    name = "private_handle_accessor",
    srcs = [
        "internal/private_handle_accessor.cc",
    ],
    hdrs = [
        "internal/private_handle_accessor.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//absl/flags:__pkg__",
    ],
    deps = [
        ":commandlineflag",
        ":commandlineflag_internal",
        "//absl/base:config",
        "//absl/strings",
    ],
)

cc_library(
    name = "reflection",
    srcs = [
        "reflection.cc",
    ],
    hdrs = [
        "internal/registry.h",
        "reflection.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":commandlineflag",
        ":commandlineflag_internal",
        ":config",
        ":private_handle_accessor",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:no_destructor",
        "//absl/container:flat_hash_map",
        "//absl/strings",
        "//absl/synchronization",
    ],
)

cc_library(
    name = "flag_internal",
    srcs = [
        "internal/flag.cc",
    ],
    hdrs = [
        "internal/flag.h",
        "internal/sequence_lock.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = ["//absl/base:__subpackages__"],
    deps = [
        ":commandlineflag",
        ":commandlineflag_internal",
        ":config",
        ":marshalling",
        ":reflection",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "//absl/base:no_destructor",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/strings",
        "//absl/synchronization",
        "//absl/utility",
    ],
)

cc_library(
    name = "flag",
    hdrs = [
        "declare.h",
        "flag.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":commandlineflag",
        ":config",
        ":flag_internal",
        ":reflection",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/strings",
    ],
)

cc_library(
    name = "usage_internal",
    srcs = [
        "internal/usage.cc",
    ],
    hdrs = [
        "internal/usage.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//absl/flags:__pkg__",
    ],
    deps = [
        ":commandlineflag",
        ":config",
        ":flag",
        ":flag_internal",
        ":path_util",
        ":private_handle_accessor",
        ":program_name",
        ":reflection",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:no_destructor",
        "//absl/strings",
        "//absl/synchronization",
    ],
)

cc_library(
    name = "usage",
    srcs = [
        "usage.cc",
    ],
    hdrs = [
        "usage.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":usage_internal",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
        "//absl/strings",
        "//absl/synchronization",
    ],
)

cc_library(
    name = "parse",
    srcs = ["parse.cc"],
    hdrs = [
        "internal/parse.h",
        "parse.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":commandlineflag",
        ":commandlineflag_internal",
        ":config",
        ":flag",
        ":flag_internal",
        ":private_handle_accessor",
        ":program_name",
        ":reflection",
        ":usage",
        ":usage_internal",
        "//absl/algorithm:container",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:no_destructor",
        "//absl/strings",
        "//absl/synchronization",
    ],
)

############################################################################
# Unit tests in alphabetical order.

cc_test(
    name = "commandlineflag_test",
    size = "small",
    srcs = [
        "commandlineflag_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test:os:android",
        "no_test:os:ios",
        "no_test_android",
        "no_test_ios",
        "no_test_wasm",
    ],
    deps = [
        ":commandlineflag",
        ":config",
        ":flag",
        ":private_handle_accessor",
        ":reflection",
        "//absl/memory",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "config_test",
    size = "small",
    srcs = [
        "config_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":config",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "flag_test",
    size = "small",
    srcs = [
        "flag_test.cc",
        "flag_test_defs.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test:os:android",
        "no_test:os:ios",
        "no_test_android",
        "no_test_ios",
        "no_test_wasm",
    ],
    deps = [
        ":config",
        ":flag",
        ":flag_internal",
        ":marshalling",
        ":parse",
        ":reflection",
        "//absl/base:core_headers",
        "//absl/base:malloc_internal",
        "//absl/base:raw_logging_internal",
        "//absl/numeric:int128",
        "//absl/strings",
        "//absl/time",
        "//absl/types:optional",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_binary(
    name = "flag_benchmark",
    testonly = True,
    srcs = [
        "flag_benchmark.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = select({
        "//conditions:default": [],
    }) + ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        "flag_benchmark.lds",
        ":flag",
        ":marshalling",
        ":parse",
        ":reflection",
        "//absl/strings",
        "//absl/time",
        "//absl/types:optional",
        "@google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "marshalling_test",
    size = "small",
    srcs = [
        "marshalling_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":marshalling",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "parse_test",
    size = "small",
    timeout = "moderate",
    srcs = [
        "parse_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test:os:android",
        "no_test:os:ios",
        "no_test_android",
        "no_test_ios",
        "no_test_wasm",
    ],
    deps = [
        ":config",
        ":flag",
        ":parse",
        ":reflection",
        ":usage_internal",
        "//absl/base:scoped_set_env",
        "//absl/log",
        "//absl/strings",
        "//absl/types:span",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "path_util_test",
    size = "small",
    srcs = [
        "internal/path_util_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":path_util",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "program_name_test",
    size = "small",
    srcs = [
        "internal/program_name_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test_fuchsia_x64",
        "no_test_wasm",
    ],
    deps = [
        ":program_name",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "reflection_test",
    size = "small",
    srcs = [
        "reflection_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test:os:android",
        "no_test:os:ios",
        "no_test_android",
        "no_test_ios",
        "no_test_wasm",
    ],
    deps = [
        ":config",
        ":flag",
        ":reflection",
        "//absl/memory",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "sequence_lock_test",
    size = "small",
    timeout = "moderate",
    srcs = [
        "internal/sequence_lock_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    shard_count = 31,
    tags = ["no_test_wasm"],
    deps = [
        ":flag_internal",
        "//absl/base",
        "//absl/container:fixed_array",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "usage_config_test",
    size = "small",
    srcs = [
        "usage_config_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":config",
        ":path_util",
        ":program_name",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "usage_test",
    size = "small",
    srcs = [
        "internal/usage_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test:os:android",
        "no_test:os:ios",
        "no_test_android",
        "no_test_ios",
        "no_test_wasm",
    ],
    deps = [
        ":config",
        ":flag",
        ":parse",
        ":path_util",
        ":program_name",
        ":reflection",
        ":usage",
        ":usage_internal",
        "//absl/strings",
        "@googletest//:gtest",
    ],
)
