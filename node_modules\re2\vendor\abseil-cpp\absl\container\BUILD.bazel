#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_DEFAULT_LINKOPTS",
    "ABSL_TEST_COPTS",
)

package(
    default_visibility = ["//visibility:public"],
    features = [
        "header_modules",
        "layering_check",
        "parse_headers",
    ],
)

licenses(["notice"])

cc_library(
    name = "compressed_tuple",
    hdrs = ["internal/compressed_tuple.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/utility",
    ],
)

cc_test(
    name = "compressed_tuple_test",
    srcs = ["internal/compressed_tuple_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":compressed_tuple",
        ":test_instance_tracker",
        "//absl/memory",
        "//absl/types:any",
        "//absl/types:optional",
        "//absl/utility",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "fixed_array",
    hdrs = ["fixed_array.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":compressed_tuple",
        "//absl/algorithm",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "//absl/base:throw_delegate",
        "//absl/memory",
    ],
)

cc_test(
    name = "fixed_array_test",
    srcs = ["fixed_array_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":fixed_array",
        ":test_allocator",
        "//absl/base:config",
        "//absl/base:exception_testing",
        "//absl/hash:hash_testing",
        "//absl/memory",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "fixed_array_exception_safety_test",
    srcs = ["fixed_array_exception_safety_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":fixed_array",
        "//absl/base:config",
        "//absl/base:exception_safety_testing",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_binary(
    name = "fixed_array_benchmark",
    testonly = True,
    srcs = ["fixed_array_benchmark.cc"],
    copts = ABSL_TEST_COPTS + ["$(STACK_FRAME_UNLIMITED)"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    deps = [
        ":fixed_array",
        "@google_benchmark//:benchmark_main",
    ],
)

cc_library(
    name = "inlined_vector_internal",
    hdrs = ["internal/inlined_vector.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":compressed_tuple",
        "//absl/base:base_internal",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/types:span",
    ],
)

cc_library(
    name = "inlined_vector",
    hdrs = ["inlined_vector.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":inlined_vector_internal",
        "//absl/algorithm",
        "//absl/base:core_headers",
        "//absl/base:throw_delegate",
        "//absl/memory",
        "//absl/meta:type_traits",
    ],
)

cc_library(
    name = "test_allocator",
    testonly = True,
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    textual_hdrs = ["internal/test_allocator.h"],
    visibility = ["//visibility:private"],
)

cc_test(
    name = "inlined_vector_test",
    srcs = ["inlined_vector_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":inlined_vector",
        ":test_allocator",
        ":test_instance_tracker",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:exception_testing",
        "//absl/hash:hash_testing",
        "//absl/log:check",
        "//absl/memory",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_binary(
    name = "inlined_vector_benchmark",
    testonly = True,
    srcs = ["inlined_vector_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    deps = [
        ":inlined_vector",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
        "//absl/strings",
        "@google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "inlined_vector_exception_safety_test",
    srcs = ["inlined_vector_exception_safety_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":inlined_vector",
        "//absl/base:config",
        "//absl/base:exception_safety_testing",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "test_instance_tracker",
    testonly = True,
    srcs = ["internal/test_instance_tracker.cc"],
    hdrs = ["internal/test_instance_tracker.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//absl:__subpackages__",
    ],
    deps = ["//absl/types:compare"],
)

cc_test(
    name = "test_instance_tracker_test",
    srcs = ["internal/test_instance_tracker_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":test_instance_tracker",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

NOTEST_TAGS_MOBILE = [
    "no_test_android_arm",
    "no_test_android_arm64",
    "no_test_android_x86",
    "no_test_ios_x86_64",
]

cc_library(
    name = "flat_hash_map",
    hdrs = ["flat_hash_map.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":container_memory",
        ":hash_container_defaults",
        ":raw_hash_map",
        "//absl/algorithm:container",
        "//absl/base:core_headers",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "flat_hash_map_test",
    srcs = ["flat_hash_map_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["no_test_loonix"],
    deps = [
        ":flat_hash_map",
        ":hash_generator_testing",
        ":hash_policy_testing",
        ":test_allocator",
        ":unordered_map_constructor_test",
        ":unordered_map_lookup_test",
        ":unordered_map_members_test",
        ":unordered_map_modifiers_test",
        "//absl/base:config",
        "//absl/log:check",
        "//absl/meta:type_traits",
        "//absl/types:any",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "flat_hash_set",
    hdrs = ["flat_hash_set.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":container_memory",
        ":hash_container_defaults",
        ":raw_hash_set",
        "//absl/algorithm:container",
        "//absl/base:core_headers",
        "//absl/memory",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "flat_hash_set_test",
    srcs = ["flat_hash_set_test.cc"],
    copts = ABSL_TEST_COPTS + ["-DUNORDERED_SET_CXX17"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["no_test_loonix"],
    deps = [
        ":container_memory",
        ":flat_hash_set",
        ":hash_container_defaults",
        ":hash_generator_testing",
        ":test_allocator",
        ":unordered_set_constructor_test",
        ":unordered_set_lookup_test",
        ":unordered_set_members_test",
        ":unordered_set_modifiers_test",
        "//absl/base:config",
        "//absl/hash",
        "//absl/log:check",
        "//absl/memory",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "node_hash_map",
    hdrs = ["node_hash_map.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":container_memory",
        ":hash_container_defaults",
        ":node_slot_policy",
        ":raw_hash_map",
        "//absl/algorithm:container",
        "//absl/base:core_headers",
        "//absl/memory",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "node_hash_map_test",
    srcs = ["node_hash_map_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["no_test_loonix"],
    deps = [
        ":hash_policy_testing",
        ":node_hash_map",
        ":tracked",
        ":unordered_map_constructor_test",
        ":unordered_map_lookup_test",
        ":unordered_map_members_test",
        ":unordered_map_modifiers_test",
        "//absl/base:config",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "node_hash_set",
    hdrs = ["node_hash_set.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":container_memory",
        ":hash_container_defaults",
        ":node_slot_policy",
        ":raw_hash_set",
        "//absl/algorithm:container",
        "//absl/base:core_headers",
        "//absl/memory",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "node_hash_set_test",
    srcs = ["node_hash_set_test.cc"],
    copts = ABSL_TEST_COPTS + ["-DUNORDERED_SET_CXX17"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["no_test_loonix"],
    deps = [
        ":hash_generator_testing",
        ":hash_policy_testing",
        ":node_hash_set",
        ":unordered_set_constructor_test",
        ":unordered_set_lookup_test",
        ":unordered_set_members_test",
        ":unordered_set_modifiers_test",
        "//absl/base:config",
        "//absl/memory",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "container_memory",
    hdrs = ["internal/container_memory.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/utility",
    ],
)

cc_test(
    name = "container_memory_test",
    srcs = ["internal/container_memory_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["no_test_loonix"],
    deps = [
        ":container_memory",
        ":test_instance_tracker",
        "//absl/base:no_destructor",
        "//absl/meta:type_traits",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "hash_function_defaults",
    hdrs = ["internal/hash_function_defaults.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//visibility:private",
    ],
    deps = [
        ":common",
        "//absl/base:config",
        "//absl/hash",
        "//absl/meta:type_traits",
        "//absl/strings",
        "//absl/strings:cord",
    ],
)

cc_library(
    name = "hash_container_defaults",
    hdrs = ["hash_container_defaults.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hash_function_defaults",
        "//absl/base:config",
    ],
)

cc_test(
    name = "hash_function_defaults_test",
    srcs = ["internal/hash_function_defaults_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = NOTEST_TAGS_MOBILE + ["no_test_loonix"],
    deps = [
        ":flat_hash_map",
        ":flat_hash_set",
        ":hash_function_defaults",
        "//absl/hash",
        "//absl/random",
        "//absl/strings",
        "//absl/strings:cord",
        "//absl/strings:cord_test_helpers",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "hash_generator_testing",
    testonly = True,
    srcs = ["internal/hash_generator_testing.cc"],
    hdrs = ["internal/hash_generator_testing.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hash_policy_testing",
        "//absl/base:no_destructor",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/strings",
    ],
)

cc_library(
    name = "hash_policy_testing",
    testonly = True,
    hdrs = ["internal/hash_policy_testing.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/hash",
        "//absl/strings",
    ],
)

cc_test(
    name = "hash_policy_testing_test",
    srcs = ["internal/hash_policy_testing_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hash_policy_testing",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "hash_policy_traits",
    hdrs = ["internal/hash_policy_traits.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":common_policy_traits",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "hash_policy_traits_test",
    srcs = ["internal/hash_policy_traits_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":container_memory",
        ":hash_policy_traits",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "common_policy_traits",
    hdrs = ["internal/common_policy_traits.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = ["//visibility:private"],
    deps = ["//absl/meta:type_traits"],
)

cc_test(
    name = "common_policy_traits_test",
    srcs = ["internal/common_policy_traits_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":common_policy_traits",
        "//absl/base:config",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "hashtable_debug",
    hdrs = ["internal/hashtable_debug.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hashtable_debug_hooks",
    ],
)

cc_library(
    name = "hashtable_debug_hooks",
    hdrs = ["internal/hashtable_debug_hooks.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
    ],
)

cc_library(
    name = "hashtablez_sampler",
    srcs = [
        "internal/hashtablez_sampler.cc",
        "internal/hashtablez_sampler_force_weak_definition.cc",
    ],
    hdrs = ["internal/hashtablez_sampler.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:no_destructor",
        "//absl/base:raw_logging_internal",
        "//absl/debugging:stacktrace",
        "//absl/memory",
        "//absl/profiling:exponential_biased",
        "//absl/profiling:sample_recorder",
        "//absl/synchronization",
        "//absl/time",
        "//absl/utility",
    ],
)

cc_test(
    name = "hashtablez_sampler_test",
    srcs = ["internal/hashtablez_sampler_test.cc"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test_wasm",
    ],
    deps = [
        ":hashtablez_sampler",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/profiling:sample_recorder",
        "//absl/synchronization",
        "//absl/synchronization:thread_pool",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "node_slot_policy",
    hdrs = ["internal/node_slot_policy.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = ["//absl/base:config"],
)

cc_test(
    name = "node_slot_policy_test",
    srcs = ["internal/node_slot_policy_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hash_policy_traits",
        ":node_slot_policy",
        "//absl/base:config",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "raw_hash_map",
    hdrs = ["internal/raw_hash_map.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":container_memory",
        ":raw_hash_set",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:throw_delegate",
    ],
)

cc_library(
    name = "common",
    hdrs = ["internal/common.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/meta:type_traits",
        "//absl/types:optional",
    ],
)

cc_library(
    name = "raw_hash_set",
    srcs = ["internal/raw_hash_set.cc"],
    hdrs = ["internal/raw_hash_set.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":common",
        ":common_policy_traits",
        ":compressed_tuple",
        ":container_memory",
        ":hash_function_defaults",
        ":hash_policy_traits",
        ":hashtable_debug_hooks",
        ":hashtablez_sampler",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "//absl/base:endian",
        "//absl/base:prefetch",
        "//absl/base:raw_logging_internal",
        "//absl/hash",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/numeric:bits",
        "//absl/utility",
    ],
)

cc_test(
    name = "raw_hash_set_test",
    srcs = ["internal/raw_hash_set_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkstatic = 1,
    tags = NOTEST_TAGS_MOBILE + [
        "no_test_loonix",
        # TODO(b/237097643): investigate race and remove
        "noarm_gemu",
    ],
    deps = [
        ":container_memory",
        ":flat_hash_map",
        ":flat_hash_set",
        ":hash_function_defaults",
        ":hash_policy_testing",
        ":hashtable_debug",
        ":hashtablez_sampler",
        ":node_hash_set",
        ":raw_hash_set",
        ":test_allocator",
        ":test_instance_tracker",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:prefetch",
        "//absl/functional:function_ref",
        "//absl/hash",
        "//absl/log",
        "//absl/log:check",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/strings",
        "//absl/types:optional",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_binary(
    name = "raw_hash_set_benchmark",
    testonly = True,
    srcs = ["internal/raw_hash_set_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":container_memory",
        ":hash_function_defaults",
        ":raw_hash_set",
        "//absl/base:raw_logging_internal",
        "//absl/random",
        "//absl/strings:str_format",
        "@google_benchmark//:benchmark_main",
    ],
)

cc_binary(
    name = "raw_hash_set_probe_benchmark",
    testonly = True,
    srcs = ["internal/raw_hash_set_probe_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = select({
        "//conditions:default": [],
    }) + ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":flat_hash_map",
        ":hash_function_defaults",
        ":hashtable_debug",
        ":raw_hash_set",
        "//absl/base:no_destructor",
        "//absl/random",
        "//absl/random:distributions",
        "//absl/strings",
        "//absl/strings:str_format",
        "//absl/types:optional",
    ],
)

cc_test(
    name = "raw_hash_set_allocator_test",
    size = "small",
    srcs = ["internal/raw_hash_set_allocator_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":container_memory",
        ":raw_hash_set",
        ":tracked",
        "//absl/base:config",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "layout",
    hdrs = ["internal/layout.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/debugging:demangle_internal",
        "//absl/meta:type_traits",
        "//absl/strings",
        "//absl/types:span",
        "//absl/utility",
    ],
)

cc_test(
    name = "layout_test",
    size = "small",
    srcs = ["internal/layout_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = NOTEST_TAGS_MOBILE + ["no_test_loonix"],
    visibility = ["//visibility:private"],
    deps = [
        ":layout",
        "//absl/base:config",
        "//absl/log:check",
        "//absl/types:span",
        "//absl/utility",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_binary(
    name = "layout_benchmark",
    testonly = True,
    srcs = ["internal/layout_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":layout",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
        "@google_benchmark//:benchmark_main",
    ],
)

cc_library(
    name = "tracked",
    testonly = True,
    hdrs = ["internal/tracked.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
    ],
)

cc_library(
    name = "unordered_map_constructor_test",
    testonly = True,
    hdrs = ["internal/unordered_map_constructor_test.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hash_generator_testing",
        ":hash_policy_testing",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "unordered_map_lookup_test",
    testonly = True,
    hdrs = ["internal/unordered_map_lookup_test.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hash_generator_testing",
        ":hash_policy_testing",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "unordered_map_modifiers_test",
    testonly = True,
    hdrs = ["internal/unordered_map_modifiers_test.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hash_generator_testing",
        ":hash_policy_testing",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "unordered_set_constructor_test",
    testonly = True,
    hdrs = ["internal/unordered_set_constructor_test.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hash_generator_testing",
        ":hash_policy_testing",
        "//absl/meta:type_traits",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "unordered_set_members_test",
    testonly = True,
    hdrs = ["internal/unordered_set_members_test.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/meta:type_traits",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "unordered_map_members_test",
    testonly = True,
    hdrs = ["internal/unordered_map_members_test.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/meta:type_traits",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "unordered_set_lookup_test",
    testonly = True,
    hdrs = ["internal/unordered_set_lookup_test.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hash_generator_testing",
        ":hash_policy_testing",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "unordered_set_modifiers_test",
    testonly = True,
    hdrs = ["internal/unordered_set_modifiers_test.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":hash_generator_testing",
        ":hash_policy_testing",
        "@googletest//:gtest",
    ],
)

cc_test(
    name = "unordered_set_test",
    srcs = ["internal/unordered_set_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["no_test_loonix"],
    deps = [
        ":unordered_set_constructor_test",
        ":unordered_set_lookup_test",
        ":unordered_set_members_test",
        ":unordered_set_modifiers_test",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "unordered_map_test",
    srcs = ["internal/unordered_map_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["no_test_loonix"],
    deps = [
        ":unordered_map_constructor_test",
        ":unordered_map_lookup_test",
        ":unordered_map_members_test",
        ":unordered_map_modifiers_test",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "sample_element_size_test",
    srcs = ["sample_element_size_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["no_test_loonix"],
    visibility = ["//visibility:private"],
    deps = [
        ":flat_hash_map",
        ":flat_hash_set",
        ":node_hash_map",
        ":node_hash_set",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "btree",
    srcs = [
        "internal/btree.h",
        "internal/btree_container.h",
    ],
    hdrs = [
        "btree_map.h",
        "btree_set.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        ":common_policy_traits",
        ":compressed_tuple",
        ":container_memory",
        ":layout",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
        "//absl/base:throw_delegate",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/strings",
        "//absl/strings:cord",
        "//absl/types:compare",
    ],
)

cc_library(
    name = "btree_test_common",
    testonly = True,
    hdrs = ["btree_test.h"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":btree",
        ":flat_hash_set",
        "//absl/strings",
        "//absl/strings:cord",
        "//absl/time",
    ],
)

cc_test(
    name = "btree_test",
    size = "large",
    srcs = [
        "btree_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    shard_count = 10,
    tags = [
        "no_test:os:ios",
        "no_test_ios",
        "no_test_wasm",
    ],
    visibility = ["//visibility:private"],
    deps = [
        ":btree",
        ":btree_test_common",
        ":test_allocator",
        ":test_instance_tracker",
        "//absl/algorithm:container",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
        "//absl/flags:flag",
        "//absl/hash:hash_testing",
        "//absl/memory",
        "//absl/random",
        "//absl/strings",
        "//absl/types:compare",
        "//absl/types:optional",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_binary(
    name = "btree_benchmark",
    testonly = True,
    srcs = [
        "btree_benchmark.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":btree",
        ":btree_test_common",
        ":flat_hash_map",
        ":flat_hash_set",
        ":hashtable_debug",
        "//absl/algorithm:container",
        "//absl/base:raw_logging_internal",
        "//absl/hash",
        "//absl/log",
        "//absl/memory",
        "//absl/random",
        "//absl/strings:cord",
        "//absl/strings:str_format",
        "//absl/time",
        "@google_benchmark//:benchmark_main",
        "@googletest//:gtest",
    ],
)
