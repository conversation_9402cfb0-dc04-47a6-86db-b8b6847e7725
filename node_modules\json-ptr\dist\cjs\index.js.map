{"version": 3, "file": "index.js", "sources": ["../../src/util.ts", "../../src/pointer.ts"], "sourcesContent": ["import {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>ointer,\n  UriFragment<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  RelativeJsonPointer,\n  PathSegment,\n  PathSegments,\n  Decoder,\n} from './types';\n\nexport function replace(source: string, find: string, repl: string): string {\n  let res = '';\n  let rem = source;\n  let beg = 0;\n  let end = -1;\n  while ((end = rem.indexOf(find)) > -1) {\n    res += source.substring(beg, beg + end) + repl;\n    rem = rem.substring(end + find.length, rem.length);\n    beg += end + find.length;\n  }\n  if (rem.length > 0) {\n    res += source.substring(source.length - rem.length, source.length);\n  }\n  return res;\n}\n\nexport function decodeFragmentSegments(segments: PathSegments): PathSegments {\n  let i = -1;\n  const len = segments.length;\n  const res = new Array(len);\n  while (++i < len) {\n    if (typeof segments[i] === 'string') {\n      res[i] = replace(\n        replace(decodeURIComponent(segments[i] as string), '~1', '/'),\n        '~0',\n        '~',\n      );\n    } else {\n      res[i] = segments[i];\n    }\n  }\n  return res;\n}\n\nexport function encodeFragmentSegments(segments: PathSegments): PathSegments {\n  let i = -1;\n  const len = segments.length;\n  const res = new Array(len);\n  while (++i < len) {\n    if (typeof segments[i] === 'string') {\n      res[i] = encodeURIComponent(\n        replace(replace(segments[i] as string, '~', '~0'), '/', '~1'),\n      );\n    } else {\n      res[i] = segments[i];\n    }\n  }\n  return res;\n}\n\nexport function decodePointerSegments(segments: PathSegments): PathSegments {\n  let i = -1;\n  const len = segments.length;\n  const res = new Array(len);\n  while (++i < len) {\n    if (typeof segments[i] === 'string') {\n      res[i] = replace(replace(segments[i] as string, '~1', '/'), '~0', '~');\n    } else {\n      res[i] = segments[i];\n    }\n  }\n  return res;\n}\n\nexport function encodePointerSegments(segments: PathSegments): PathSegments {\n  let i = -1;\n  const len = segments.length;\n  const res = new Array(len);\n  while (++i < len) {\n    if (typeof segments[i] === 'string') {\n      res[i] = replace(replace(segments[i] as string, '~', '~0'), '/', '~1');\n    } else {\n      res[i] = segments[i];\n    }\n  }\n  return res;\n}\n\nexport function decodePointer(ptr: Pointer): PathSegments {\n  if (typeof ptr !== 'string') {\n    throw new TypeError(\n      'Invalid type: JSON Pointers are represented as strings.',\n    );\n  }\n  if (ptr.length === 0) {\n    return [];\n  }\n  if (ptr[0] !== '/') {\n    throw new ReferenceError(\n      'Invalid JSON Pointer syntax. Non-empty pointer must begin with a solidus `/`.',\n    );\n  }\n  return decodePointerSegments(ptr.substring(1).split('/'));\n}\n\nexport function encodePointer(path: PathSegments): JsonStringPointer {\n  if (!path || (path && !Array.isArray(path))) {\n    throw new TypeError('Invalid type: path must be an array of segments.');\n  }\n  if (path.length === 0) {\n    return '';\n  }\n  return '/'.concat(encodePointerSegments(path).join('/'));\n}\n\nexport function decodeUriFragmentIdentifier(\n  ptr: UriFragmentIdentifierPointer,\n): PathSegments {\n  if (typeof ptr !== 'string') {\n    throw new TypeError(\n      'Invalid type: JSON Pointers are represented as strings.',\n    );\n  }\n  if (ptr.length === 0 || ptr[0] !== '#') {\n    throw new ReferenceError(\n      'Invalid JSON Pointer syntax; URI fragment identifiers must begin with a hash.',\n    );\n  }\n  if (ptr.length === 1) {\n    return [];\n  }\n  if (ptr[1] !== '/') {\n    throw new ReferenceError('Invalid JSON Pointer syntax.');\n  }\n  return decodeFragmentSegments(ptr.substring(2).split('/'));\n}\n\nexport function encodeUriFragmentIdentifier(\n  path: PathSegments,\n): UriFragmentIdentifierPointer {\n  if (!path || (path && !Array.isArray(path))) {\n    throw new TypeError('Invalid type: path must be an array of segments.');\n  }\n  if (path.length === 0) {\n    return '#';\n  }\n  return '#/'.concat(encodeFragmentSegments(path).join('/'));\n}\n\nconst InvalidRelativePointerError =\n  'Invalid Relative JSON Pointer syntax. Relative pointer must begin with a non-negative integer, followed by either the number sign (#), or a JSON Pointer.';\n\nexport function decodeRelativePointer(ptr: RelativeJsonPointer): PathSegments {\n  if (typeof ptr !== 'string') {\n    throw new TypeError(\n      'Invalid type: Relative JSON Pointers are represented as strings.',\n    );\n  }\n  if (ptr.length === 0) {\n    // https://tools.ietf.org/id/draft-handrews-relative-json-pointer-00.html#rfc.section.3\n    throw new ReferenceError(InvalidRelativePointerError);\n  }\n  const segments = ptr.split('/');\n  let first = segments[0];\n  // It is a name reference; strip the hash.\n  if (first[first.length - 1] == '#') {\n    if (segments.length > 1) {\n      throw new ReferenceError(InvalidRelativePointerError);\n    }\n    first = first.substr(0, first.length - 1);\n  }\n  let i = -1;\n  const len = first.length;\n  while (++i < len) {\n    if (first[i] < '0' || first[i] > '9') {\n      throw new ReferenceError(InvalidRelativePointerError);\n    }\n  }\n  const path: unknown[] = decodePointerSegments(segments.slice(1));\n  path.unshift(segments[0]);\n  return path as PathSegments;\n}\n\nexport function toArrayIndexReference(\n  arr: readonly unknown[],\n  idx: PathSegment,\n): number {\n  if (typeof idx === 'number') return idx;\n  const len = idx.length;\n  if (!len) return -1;\n  let cursor = 0;\n  if (len === 1 && idx[0] === '-') {\n    if (!Array.isArray(arr)) {\n      return 0;\n    }\n    return arr.length;\n  }\n  while (++cursor < len) {\n    if (idx[cursor] < '0' || idx[cursor] > '9') {\n      return -1;\n    }\n  }\n  return parseInt(idx, 10);\n}\n\nexport type Dereference = (it: unknown) => unknown;\n\nexport function compilePointerDereference(path: PathSegments): Dereference {\n  let body = \"if (typeof(it) !== 'undefined'\";\n  if (path.length === 0) {\n    return (it): unknown => it;\n  }\n  body = path.reduce((body, _, i) => {\n    return (\n      body +\n      \"\\n\\t&& it !== null && typeof((it = it['\" +\n      replace(replace(path[i] + '', '\\\\', '\\\\\\\\'), \"'\", \"\\\\'\") +\n      \"'])) !== 'undefined'\"\n    );\n  }, \"if (typeof(it) !== 'undefined'\") as string;\n  body = body + ') {\\n\\treturn it;\\n }';\n  // eslint-disable-next-line no-new-func\n  return new Function('it', body) as Dereference;\n}\n\nexport function setValueAtPath(\n  target: unknown,\n  val: unknown,\n  path: PathSegments,\n  force = false,\n): unknown {\n  if (path.length === 0) {\n    throw new Error('Cannot set the root object; assign it directly.');\n  }\n  if (typeof target === 'undefined') {\n    throw new TypeError('Cannot set values on undefined');\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let it: any = target;\n  const len = path.length;\n  const end = path.length - 1;\n  let step: PathSegment;\n  let cursor = -1;\n  let rem: unknown;\n  let p: number;\n  while (++cursor < len) {\n    step = path[cursor];\n    if (typeof step !== 'string' && typeof step !== 'number') {\n      throw new TypeError('PathSegments must be a string or a number.');\n    }\n    if (\n      // Reconsider this strategy. It disallows legitimate structures on\n      // non - objects, or more precisely, on objects not derived from a class\n      // or constructor function.\n      step === '__proto__' ||\n      step === 'constructor' ||\n      step === 'prototype'\n    ) {\n      throw new Error('Attempted prototype pollution disallowed.');\n    }\n    if (Array.isArray(it)) {\n      if (step === '-' && cursor === end) {\n        it.push(val);\n        return undefined;\n      }\n      p = toArrayIndexReference(it, step);\n      if (it.length > p) {\n        if (cursor === end) {\n          rem = it[p];\n          it[p] = val;\n          break;\n        }\n        it = it[p];\n      } else if (cursor === end && p === it.length) {\n        if (force) {\n          it.push(val);\n          return undefined;\n        }\n      } else if (force) {\n        it = it[p] = cursor === end ? val : {};\n      }\n    } else {\n      if (typeof it[step] === 'undefined') {\n        if (force) {\n          if (cursor === end) {\n            it[step] = val;\n            return undefined;\n          }\n          // if the next step is an array index, this step should be an array.\n          const n = Number(path[cursor + 1]);\n          if (\n            Number.isInteger(n) &&\n            toArrayIndexReference(it[step], n) !== -1\n          ) {\n            it = it[step] = [];\n            continue;\n          }\n          it = it[step] = {};\n          continue;\n        }\n        return undefined;\n      }\n      if (cursor === end) {\n        rem = it[step];\n        it[step] = val;\n        break;\n      }\n      it = it[step];\n    }\n  }\n  return rem;\n}\n\nexport function unsetValueAtPath(target: unknown, path: PathSegments): unknown {\n  if (path.length === 0) {\n    throw new Error('Cannot unset the root object; assign it directly.');\n  }\n  if (typeof target === 'undefined') {\n    throw new TypeError('Cannot unset values on undefined');\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let it: any = target;\n  const len = path.length;\n  const end = path.length - 1;\n  let step: PathSegment;\n  let cursor = -1;\n  let rem: unknown;\n  let p: number;\n  while (++cursor < len) {\n    step = path[cursor];\n    if (typeof step !== 'string' && typeof step !== 'number') {\n      throw new TypeError('PathSegments must be a string or a number.');\n    }\n    if (\n      step === '__proto__' ||\n      step === 'constructor' ||\n      step === 'prototype'\n    ) {\n      throw new Error('Attempted prototype pollution disallowed.');\n    }\n    if (Array.isArray(it)) {\n      p = toArrayIndexReference(it, step);\n      if (p >= it.length) return undefined;\n      if (cursor === end) {\n        rem = it[p];\n        delete it[p];\n        break;\n      }\n      it = it[p];\n    } else {\n      if (typeof it[step] === 'undefined') {\n        return undefined;\n      }\n      if (cursor === end) {\n        rem = it[step];\n        delete it[step];\n        break;\n      }\n      it = it[step];\n    }\n  }\n  return rem;\n}\n\nexport function looksLikeFragment(ptr: Pointer): boolean {\n  return typeof ptr === 'string' && ptr.length > 0 && ptr[0] === '#';\n}\n\nexport function pickDecoder(ptr: Pointer): Decoder {\n  return looksLikeFragment(ptr) ? decodeUriFragmentIdentifier : decodePointer;\n}\n\nexport function decodePtrInit(ptr: Pointer | PathSegments): PathSegments {\n  return Array.isArray(ptr)\n    ? ptr.slice(0)\n    : pickDecoder(ptr as Pointer)(ptr as Pointer);\n}\n", "import {\n  Dereference,\n  decodePtrInit,\n  compilePointerDereference,\n  setValueAtPath,\n  encodePointer,\n  encodeUriFragmentIdentifier,\n  pickDecoder,\n  unsetValueAtPath,\n  decodeRelativePointer,\n} from './util';\nimport {\n  JsonStringPointer,\n  UriFragmentIdentifierPointer,\n  Pointer,\n  RelativeJsonPointer,\n  PathSegments,\n  Encoder,\n  JsonStringPointerListItem,\n  UriFragmentIdentifierPointerListItem,\n} from './types';\n\n/**\n * Determines if the value is an object (not null)\n * @param value the value\n * @returns true if the value is a non-null object; otherwise false.\n *\n * @hidden\n */\nfunction isObject(value: unknown): boolean {\n  return typeof value === 'object' && value !== null;\n}\n\n/**\n * Signature of visitor functions, used with [[JsonPointer.visit]] method. Visitors are callbacks invoked for every segment/branch of a target's object graph.\n *\n * Tree descent occurs in-order, breadth first.\n */\nexport type Visitor = (ptr: <PERSON><PERSON><PERSON>tringPointer, val: unknown) => void;\n\n/** @hidden */\ninterface Item {\n  obj: unknown;\n  path: PathSegments;\n}\n\n/** @hidden */\nfunction shouldDescend(obj: unknown): boolean {\n  return isObject(obj) && !JsonReference.isReference(obj);\n}\n/** @hidden */\nfunction descendingVisit(\n  target: unknown,\n  visitor: Visitor,\n  encoder: Encoder,\n): void {\n  const distinctObjects = new Map<unknown, JsonPointer>();\n  const q: Item[] = [{ obj: target, path: [] }];\n  while (q.length) {\n    const { obj, path } = q.shift() as Item;\n    visitor(encoder(path), obj);\n    if (shouldDescend(obj)) {\n      distinctObjects.set(\n        obj,\n        new JsonPointer(encodeUriFragmentIdentifier(path)),\n      );\n      if (!Array.isArray(obj)) {\n        const keys = Object.keys(obj as Record<string, unknown>);\n        const len = keys.length;\n        let i = -1;\n        while (++i < len) {\n          const it = (obj as Record<string, unknown>)[keys[i]];\n          if (isObject(it) && distinctObjects.has(it)) {\n            q.push({\n              obj: new JsonReference(distinctObjects.get(it) as JsonPointer),\n              path: path.concat(keys[i]),\n            });\n          } else {\n            q.push({\n              obj: it,\n              path: path.concat(keys[i]),\n            });\n          }\n        }\n      } else {\n        // handleArray\n        let j = -1;\n        const len = obj.length;\n        while (++j < len) {\n          const it = obj[j];\n          if (isObject(it) && distinctObjects.has(it)) {\n            q.push({\n              obj: new JsonReference(distinctObjects.get(it) as JsonPointer),\n              path: path.concat([j + '']),\n            });\n          } else {\n            q.push({\n              obj: it,\n              path: path.concat([j + '']),\n            });\n          }\n        }\n      }\n    }\n  }\n}\n\n/** @hidden */\nconst $ptr = Symbol('pointer');\n/** @hidden */\nconst $frg = Symbol('fragmentId');\n/** @hidden */\nconst $get = Symbol('getter');\n\n/**\n * Represents a JSON Pointer, capable of getting and setting the value on target\n * objects at the pointer's location.\n *\n * While there are static variants for most operations, our recommendation is\n * to use the instance level methods, which enables you avoid repeated\n * compiling/emitting transient accessors. Take a look at the speed comparisons\n * for our justification.\n *\n * In most cases, you should create and reuse instances of JsonPointer within\n * scope that makes sense for your app. We often create constants for frequently\n * used pointers, but your use case may vary.\n *\n * The following is a contrived example showing a function that uses pointers to\n * deal with changes in the structure of data (a version independent function):\n *\n * ```ts\n * import { JsonPointer } from 'json-ptr';\n *\n * export type SupportedVersion = '1.0' | '1.1';\n *\n * interface PrimaryGuestNamePointers {\n *   name: JsonPointer;\n *   surname: JsonPointer;\n *   honorific: JsonPointer;\n * }\n * const versions: Record<SupportedVersion, PrimaryGuestNamePointers> = {\n *   '1.0': {\n *     name: JsonPointer.create('/guests/0/name'),\n *     surname: JsonPointer.create('/guests/0/surname'),\n *     honorific: JsonPointer.create('/guests/0/honorific'),\n *   },\n *   '1.1': {\n *     name: JsonPointer.create('/primary/primaryGuest/name'),\n *     surname: JsonPointer.create('/primary/primaryGuest/surname'),\n *     honorific: JsonPointer.create('/primary/primaryGuest/honorific'),\n *   }\n * };\n *\n * interface Reservation extends Record<string, unknown> {\n *   version?: SupportedVersion;\n * }\n *\n * function primaryGuestName(reservation: Reservation): string {\n *   const pointers = versions[reservation.version || '1.0'];\n *   const name = pointers.name.get(reservation) as string;\n *   const surname = pointers.surname.get(reservation) as string;\n *   const honorific = pointers.honorific.get(reservation) as string;\n *   const names: string[] = [];\n *   if (honorific) names.push(honorific);\n *   if (name) names.push(name);\n *   if (surname) names.push(surname);\n *   return names.join(' ');\n * }\n *\n * // The original layout of a reservation (only the parts relevant to our example)\n * const reservationV1: Reservation = {\n *   guests: [{\n *     name: 'Wilbur',\n *     surname: 'Finkle',\n *     honorific: 'Mr.'\n *   }, {\n *     name: 'Wanda',\n *     surname: 'Finkle',\n *     honorific: 'Mrs.'\n *   }, {\n *     name: 'Wilma',\n *     surname: 'Finkle',\n *     honorific: 'Miss',\n *     child: true,\n *     age: 12\n *   }]\n *   // ...\n * };\n *\n * // The new layout of a reservation (only the parts relevant to our example)\n * const reservationV1_1: Reservation = {\n *   version: '1.1',\n *   primary: {\n *     primaryGuest: {\n *       name: 'Wilbur',\n *       surname: 'Finkle',\n *       honorific: 'Mr.'\n *     },\n *     additionalGuests: [{\n *       name: 'Wanda',\n *       surname: 'Finkle',\n *       honorific: 'Mrs.'\n *     }, {\n *       name: 'Wilma',\n *       surname: 'Finkle',\n *       honorific: 'Miss',\n *       child: true,\n *       age: 12\n *     }]\n *     // ...\n *   }\n *   // ...\n * };\n *\n * console.log(primaryGuestName(reservationV1));\n * console.log(primaryGuestName(reservationV1_1));\n *\n * ```\n *\n * There are many uses for pointers.\n */\nexport class JsonPointer {\n  /** @hidden */\n  private [$ptr]: JsonStringPointer;\n  /** @hidden */\n  private [$frg]: UriFragmentIdentifierPointer;\n  /** @hidden */\n  private [$get]: Dereference;\n\n  /**\n   * Factory function that creates a JsonPointer instance.\n   *\n   * ```ts\n   * const ptr = JsonPointer.create('/deeply/nested/data/0/here');\n   * ```\n   * _or_\n   * ```ts\n   * const ptr = JsonPointer.create(['deeply', 'nested', 'data', 0, 'here']);\n   * ```\n   * @param pointer the pointer or path.\n   */\n  static create(pointer: Pointer | PathSegments): JsonPointer {\n    return new JsonPointer(pointer);\n  }\n\n  /**\n   * Determines if the specified `target`'s object graph has a value at the `pointer`'s location.\n   *\n   * ```ts\n   * const target = {\n   *   first: 'second',\n   *   third: ['fourth', 'fifth', { sixth: 'seventh' }],\n   *   eighth: 'ninth'\n   * };\n   *\n   * console.log(JsonPointer.has(target, '/third/0'));\n   * // true\n   * console.log(JsonPointer.has(target, '/tenth'));\n   * // false\n   * ```\n   *\n   * @param target the target of the operation\n   * @param pointer the pointer or path\n   */\n  static has(\n    target: unknown,\n    pointer: Pointer | PathSegments | JsonPointer,\n  ): boolean {\n    if (typeof pointer === 'string' || Array.isArray(pointer)) {\n      pointer = new JsonPointer(pointer);\n    }\n    return (pointer as JsonPointer).has(target);\n  }\n\n  /**\n   * Gets the `target` object's value at the `pointer`'s location.\n   *\n   * ```ts\n   * const target = {\n   *   first: 'second',\n   *   third: ['fourth', 'fifth', { sixth: 'seventh' }],\n   *   eighth: 'ninth'\n   * };\n   *\n   * console.log(JsonPointer.get(target, '/third/2/sixth'));\n   * // seventh\n   * console.log(JsonPointer.get(target, '/tenth'));\n   * // undefined\n   * ```\n   *\n   * @param target the target of the operation\n   * @param pointer the pointer or path.\n   */\n  static get(\n    target: unknown,\n    pointer: Pointer | PathSegments | JsonPointer,\n  ): unknown {\n    if (typeof pointer === 'string' || Array.isArray(pointer)) {\n      pointer = new JsonPointer(pointer);\n    }\n    return (pointer as JsonPointer).get(target);\n  }\n\n  /**\n   * Sets the `target` object's value, as specified, at the `pointer`'s location.\n   *\n   * ```ts\n   * const target = {\n   *   first: 'second',\n   *   third: ['fourth', 'fifth', { sixth: 'seventh' }],\n   *   eighth: 'ninth'\n   * };\n   *\n   * console.log(JsonPointer.set(target, '/third/2/sixth', 'tenth'));\n   * // seventh\n   * console.log(JsonPointer.set(target, '/tenth', 'eleventh', true));\n   * // undefined\n   * console.log(JSON.stringify(target, null, ' '));\n   * // {\n   * // \"first\": \"second\",\n   * // \"third\": [\n   * //  \"fourth\",\n   * //  \"fifth\",\n   * //  {\n   * //   \"sixth\": \"tenth\"\n   * //  }\n   * // ],\n   * // \"eighth\": \"ninth\",\n   * // \"tenth\": \"eleventh\"\n   * // }\n   * ```\n   *\n   * @param target the target of the operation\n   * @param pointer the pointer or path\n   * @param val a value to write into the object graph at the specified pointer location\n   * @param force indications whether the operation should force the pointer's location into existence in the object graph.\n   *\n   * @returns the prior value at the pointer's location in the object graph.\n   */\n  static set(\n    target: unknown,\n    pointer: Pointer | PathSegments | JsonPointer,\n    val: unknown,\n    force = false,\n  ): unknown {\n    if (typeof pointer === 'string' || Array.isArray(pointer)) {\n      pointer = new JsonPointer(pointer);\n    }\n    return (pointer as JsonPointer).set(target, val, force);\n  }\n\n  /**\n   * Removes the `target` object's value at the `pointer`'s location.\n   *\n   * ```ts\n   * const target = {\n   *   first: 'second',\n   *   third: ['fourth', 'fifth', { sixth: 'seventh' }],\n   *   eighth: 'ninth'\n   * };\n   *\n   * console.log(JsonPointer.unset(target, '/third/2/sixth'));\n   * // seventh\n   * console.log(JsonPointer.unset(target, '/tenth'));\n   * // undefined\n   * console.log(JSON.stringify(target, null, ' '));\n   * // {\n   * // \"first\": \"second\",\n   * // \"third\": [\n   * //  \"fourth\",\n   * //  \"fifth\",\n   * //  {}\n   * // ],\n   * // \"eighth\": \"ninth\",\n   * // }\n   * ```\n   * @param target the target of the operation\n   * @param pointer the pointer or path\n   *\n   * @returns the value that was removed from the object graph.\n   */\n  static unset(\n    target: unknown,\n    pointer: Pointer | PathSegments | JsonPointer,\n  ): unknown {\n    if (typeof pointer === 'string' || Array.isArray(pointer)) {\n      pointer = new JsonPointer(pointer);\n    }\n    return (pointer as JsonPointer).unset(target);\n  }\n\n  /**\n   * Decodes the specified pointer into path segments.\n   * @param pointer a string representation of a JSON Pointer\n   */\n  static decode(pointer: Pointer): PathSegments {\n    return pickDecoder(pointer)(pointer);\n  }\n\n  /**\n   * Evaluates the target's object graph, calling the specified visitor for every unique pointer location discovered while walking the graph.\n   * @param target the target of the operation\n   * @param visitor a callback function invoked for each unique pointer location in the object graph\n   * @param fragmentId indicates whether the visitor should receive fragment identifiers or regular pointers\n   */\n  static visit(target: unknown, visitor: Visitor, fragmentId = false): void {\n    descendingVisit(\n      target,\n      visitor,\n      fragmentId ? encodeUriFragmentIdentifier : encodePointer,\n    );\n  }\n\n  /**\n   * Evaluates the target's object graph, returning a [[JsonStringPointerListItem]] for each location in the graph.\n   * @param target the target of the operation\n   */\n  static listPointers(target: unknown): JsonStringPointerListItem[] {\n    const res: JsonStringPointerListItem[] = [];\n    descendingVisit(\n      target,\n      (pointer, value): void => {\n        res.push({ pointer, value });\n      },\n      encodePointer,\n    );\n    return res;\n  }\n\n  /**\n   * Evaluates the target's object graph, returning a [[UriFragmentIdentifierPointerListItem]] for each location in the graph.\n   * @param target the target of the operation\n   */\n  static listFragmentIds(\n    target: unknown,\n  ): UriFragmentIdentifierPointerListItem[] {\n    const res: UriFragmentIdentifierPointerListItem[] = [];\n    descendingVisit(\n      target,\n      (fragmentId, value): void => {\n        res.push({ fragmentId, value });\n      },\n      encodeUriFragmentIdentifier,\n    );\n    return res;\n  }\n\n  /**\n   * Evaluates the target's object graph, returning a Record&lt;Pointer, unknown> populated with pointers and the corresponding values from the graph.\n   * @param target the target of the operation\n   * @param fragmentId indicates whether the results are populated with fragment identifiers rather than regular pointers\n   */\n  static flatten(\n    target: unknown,\n    fragmentId = false,\n  ): Record<Pointer, unknown> {\n    const res: Record<Pointer, unknown> = {};\n    descendingVisit(\n      target,\n      (p, v) => {\n        res[p] = v;\n      },\n      fragmentId ? encodeUriFragmentIdentifier : encodePointer,\n    );\n    return res;\n  }\n\n  /**\n   * Evaluates the target's object graph, returning a Map&lt;Pointer,unknown>  populated with pointers and the corresponding values form the graph.\n   * @param target the target of the operation\n   * @param fragmentId indicates whether the results are populated with fragment identifiers rather than regular pointers\n   */\n  static map(target: unknown, fragmentId = false): Map<Pointer, unknown> {\n    const res = new Map<Pointer, unknown>();\n    descendingVisit(\n      target,\n      res.set.bind(res),\n      fragmentId ? encodeUriFragmentIdentifier : encodePointer,\n    );\n    return res;\n  }\n\n  /**\n   * The pointer's decoded path segments.\n   */\n  public readonly path: PathSegments;\n\n  /**\n   * Creates a new instance.\n   * @param ptr a string representation of a JSON Pointer, or a decoded array of path segments.\n   */\n  constructor(ptr: Pointer | PathSegments) {\n    this.path = decodePtrInit(ptr);\n  }\n\n  /**\n   * Gets the target object's value at the pointer's location.\n   * @param target the target of the operation\n   */\n  get(target: unknown): unknown {\n    if (!this[$get]) {\n      this[$get] = compilePointerDereference(this.path);\n    }\n    return this[$get](target);\n  }\n\n  /**\n   * Sets the target object's value, as specified, at the pointer's location.\n   *\n   * If any part of the pointer's path does not exist, the operation aborts\n   * without modification, unless the caller indicates that pointer's location\n   * should be created.\n   *\n   * @param target the target of the operation\n   * @param value the value to set\n   * @param force indicates whether the pointer's location should be created if it doesn't already exist.\n   */\n  set(target: unknown, value: unknown, force = false): unknown {\n    return setValueAtPath(target, value, this.path, force);\n  }\n\n  /**\n   * Removes the target object's value at the pointer's location.\n   * @param target the target of the operation\n   *\n   * @returns the value that was removed from the object graph.\n   */\n  unset(target: unknown): unknown {\n    return unsetValueAtPath(target, this.path);\n  }\n\n  /**\n   * Determines if the specified target's object graph has a value at the pointer's location.\n   * @param target the target of the operation\n   */\n  has(target: unknown): boolean {\n    return typeof this.get(target) !== 'undefined';\n  }\n\n  /**\n   * Gets the value in the object graph that is the parent of the pointer location.\n   * @param target the target of the operation\n   */\n  parent(target: unknown): unknown {\n    const p = this.path;\n    if (p.length == 1) return undefined;\n    const parent = new JsonPointer(p.slice(0, p.length - 1));\n    return parent.get(target);\n  }\n\n  /**\n   * Creates a new JsonPointer instance, pointing to the specified relative location in the object graph.\n   * @param ptr the relative pointer (relative to this)\n   * @returns A new instance that points to the relative location.\n   */\n  relative(ptr: RelativeJsonPointer): JsonPointer {\n    const p = this.path;\n    const decoded = decodeRelativePointer(ptr) as string[];\n    const n = parseInt(decoded[0]);\n    if (n > p.length) throw new Error('Relative location does not exist.');\n    const r = p.slice(0, p.length - n).concat(decoded.slice(1));\n    if (decoded[0][decoded[0].length - 1] == '#') {\n      // It references the path segment/name, not the value\n      const name = r[r.length - 1] as string;\n      throw new Error(\n        `We won't compile a pointer that will always return '${name}'. Use JsonPointer.rel(target, ptr) instead.`,\n      );\n    }\n    return new JsonPointer(r);\n  }\n\n  /**\n   * Resolves the specified relative pointer path against the specified target object, and gets the target object's value at the relative pointer's location.\n   * @param target the target of the operation\n   * @param ptr the relative pointer (relative to this)\n   * @returns the value at the relative pointer's resolved path; otherwise undefined.\n   */\n  rel(target: unknown, ptr: RelativeJsonPointer): unknown {\n    const p = this.path;\n    const decoded = decodeRelativePointer(ptr) as string[];\n    const n = parseInt(decoded[0]);\n    if (n > p.length) {\n      // out of bounds\n      return undefined;\n    }\n    const r = p.slice(0, p.length - n).concat(decoded.slice(1));\n    const other = new JsonPointer(r);\n    if (decoded[0][decoded[0].length - 1] == '#') {\n      // It references the path segment/name, not the value\n      const name = r[r.length - 1] as string;\n      const parent = other.parent(target);\n      return Array.isArray(parent) ? parseInt(name, 10) : name;\n    }\n    return other.get(target);\n  }\n\n  /**\n   * Creates a new instance by concatenating the specified pointer's path onto this pointer's path.\n   * @param ptr the string representation of a pointer, it's decoded path, or an instance of JsonPointer indicating the additional path to concatenate onto the pointer.\n   */\n  concat(ptr: JsonPointer | Pointer | PathSegments): JsonPointer {\n    return new JsonPointer(\n      this.path.concat(\n        ptr instanceof JsonPointer ? ptr.path : decodePtrInit(ptr),\n      ),\n    );\n  }\n\n  /**\n   * This pointer's JSON Pointer encoded string representation.\n   */\n  get pointer(): JsonStringPointer {\n    if (this[$ptr] === undefined) {\n      this[$ptr] = encodePointer(this.path);\n    }\n    return this[$ptr];\n  }\n\n  /**\n   * This pointer's URI fragment identifier encoded string representation.\n   */\n  get uriFragmentIdentifier(): UriFragmentIdentifierPointer {\n    if (!this[$frg]) {\n      this[$frg] = encodeUriFragmentIdentifier(this.path);\n    }\n    return this[$frg];\n  }\n\n  /**\n   * Emits the JSON Pointer encoded string representation.\n   */\n  toString(): string {\n    return this.pointer;\n  }\n}\n\n/** @hidden */\nconst $pointer = Symbol('pointer');\n\n/**\n * A reference to a location in an object graph.\n *\n * This type is used by this module to break cycles in an object graph and to\n * reference locations that have already been visited when enumerating pointers.\n */\nexport class JsonReference {\n  /**\n   * Determines if the specified `candidate` is a JsonReference.\n   * @param candidate the candidate\n   */\n  static isReference(candidate: unknown): candidate is JsonReference {\n    if (!candidate) return false;\n    const ref = candidate as unknown as JsonReference;\n    return typeof ref.$ref === 'string' && typeof ref.resolve === 'function';\n  }\n\n  /** @hidden */\n  private readonly [$pointer]: JsonPointer;\n\n  /**\n   * A reference to a position if an object graph.\n   */\n  public readonly $ref: UriFragmentIdentifierPointer;\n\n  /**\n   * Creates a new instance.\n   * @param pointer a JSON Pointer for the reference.\n   */\n  constructor(pointer: JsonPointer | Pointer | PathSegments) {\n    this[$pointer] =\n      pointer instanceof JsonPointer ? pointer : new JsonPointer(pointer);\n    this.$ref = this[$pointer].uriFragmentIdentifier;\n  }\n\n  /**\n   * Resolves the reference against the `target` object, returning the value at\n   * the referenced pointer's location.\n   * @param target the target object\n   */\n  resolve(target: unknown): unknown {\n    return this[$pointer].get(target);\n  }\n\n  /**\n   * Gets the reference's pointer.\n   */\n  pointer(): JsonPointer {\n    return this[$pointer];\n  }\n\n  /**\n   * Gets the reference pointer's string representation (a URI fragment identifier).\n   */\n  toString(): string {\n    return this.$ref;\n  }\n}\n"], "names": [], "mappings": ";;;;SAUgB,OAAO,CAAC,MAAc,EAAE,IAAY,EAAE,IAAY;IAChE,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,GAAG,GAAG,MAAM,CAAC;IACjB,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;IACb,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;QACrC,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;QAC/C,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QACnD,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;KAC1B;IACD,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;QAClB,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;KACpE;IACD,OAAO,GAAG,CAAC;AACb,CAAC;SAEe,sBAAsB,CAAC,QAAsB;IAC3D,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC5B,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3B,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE;QAChB,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YACnC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,CACd,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAW,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,EAC7D,IAAI,EACJ,GAAG,CACJ,CAAC;SACH;aAAM;YACL,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;SACtB;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;SAEe,sBAAsB,CAAC,QAAsB;IAC3D,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC5B,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3B,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE;QAChB,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YACnC,GAAG,CAAC,CAAC,CAAC,GAAG,kBAAkB,CACzB,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAW,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAC9D,CAAC;SACH;aAAM;YACL,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;SACtB;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;SAEe,qBAAqB,CAAC,QAAsB;IAC1D,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC5B,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3B,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE;QAChB,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YACnC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAW,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;SACxE;aAAM;YACL,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;SACtB;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;SAEe,qBAAqB,CAAC,QAAsB;IAC1D,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC5B,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3B,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE;QAChB,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YACnC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAW,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;SACxE;aAAM;YACL,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;SACtB;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;SAEe,aAAa,CAAC,GAAY;IACxC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,MAAM,IAAI,SAAS,CACjB,yDAAyD,CAC1D,CAAC;KACH;IACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,EAAE,CAAC;KACX;IACD,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAClB,MAAM,IAAI,cAAc,CACtB,+EAA+E,CAChF,CAAC;KACH;IACD,OAAO,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5D,CAAC;SAEe,aAAa,CAAC,IAAkB;IAC9C,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE;QAC3C,MAAM,IAAI,SAAS,CAAC,kDAAkD,CAAC,CAAC;KACzE;IACD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,OAAO,EAAE,CAAC;KACX;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3D,CAAC;SAEe,2BAA2B,CACzC,GAAiC;IAEjC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,MAAM,IAAI,SAAS,CACjB,yDAAyD,CAC1D,CAAC;KACH;IACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC,MAAM,IAAI,cAAc,CACtB,+EAA+E,CAChF,CAAC;KACH;IACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,EAAE,CAAC;KACX;IACD,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAClB,MAAM,IAAI,cAAc,CAAC,8BAA8B,CAAC,CAAC;KAC1D;IACD,OAAO,sBAAsB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7D,CAAC;SAEe,2BAA2B,CACzC,IAAkB;IAElB,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE;QAC3C,MAAM,IAAI,SAAS,CAAC,kDAAkD,CAAC,CAAC;KACzE;IACD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,OAAO,GAAG,CAAC;KACZ;IACD,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7D,CAAC;AAED,MAAM,2BAA2B,GAC/B,2JAA2J,CAAC;SAE9I,qBAAqB,CAAC,GAAwB;IAC5D,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,MAAM,IAAI,SAAS,CACjB,kEAAkE,CACnE,CAAC;KACH;IACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;;QAEpB,MAAM,IAAI,cAAc,CAAC,2BAA2B,CAAC,CAAC;KACvD;IACD,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAChC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;;IAExB,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;QAClC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,cAAc,CAAC,2BAA2B,CAAC,CAAC;SACvD;QACD,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAC3C;IACD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;IACzB,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE;QAChB,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE;YACpC,MAAM,IAAI,cAAc,CAAC,2BAA2B,CAAC,CAAC;SACvD;KACF;IACD,MAAM,IAAI,GAAc,qBAAqB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,OAAO,IAAoB,CAAC;AAC9B,CAAC;SAEe,qBAAqB,CACnC,GAAuB,EACvB,GAAgB;IAEhB,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,OAAO,GAAG,CAAC;IACxC,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACvB,IAAI,CAAC,GAAG;QAAE,OAAO,CAAC,CAAC,CAAC;IACpB,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACvB,OAAO,CAAC,CAAC;SACV;QACD,OAAO,GAAG,CAAC,MAAM,CAAC;KACnB;IACD,OAAO,EAAE,MAAM,GAAG,GAAG,EAAE;QACrB,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,EAAE;YAC1C,OAAO,CAAC,CAAC,CAAC;SACX;KACF;IACD,OAAO,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAC3B,CAAC;SAIe,yBAAyB,CAAC,IAAkB;IAC1D,IAAI,IAAI,GAAG,gCAAgC,CAAC;IAC5C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,OAAO,CAAC,EAAE,KAAc,EAAE,CAAC;KAC5B;IACD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;QAC5B,QACE,IAAI;YACJ,yCAAyC;YACzC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;YACxD,sBAAsB,EACtB;KACH,EAAE,gCAAgC,CAAW,CAAC;IAC/C,IAAI,GAAG,IAAI,GAAG,uBAAuB,CAAC;;IAEtC,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAgB,CAAC;AACjD,CAAC;SAEe,cAAc,CAC5B,MAAe,EACf,GAAY,EACZ,IAAkB,EAClB,KAAK,GAAG,KAAK;IAEb,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;KACpE;IACD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACjC,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,CAAC;KACvD;;IAED,IAAI,EAAE,GAAQ,MAAM,CAAC;IACrB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;IACxB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5B,IAAI,IAAiB,CAAC;IACtB,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;IAChB,IAAI,GAAY,CAAC;IACjB,IAAI,CAAS,CAAC;IACd,OAAO,EAAE,MAAM,GAAG,GAAG,EAAE;QACrB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YACxD,MAAM,IAAI,SAAS,CAAC,4CAA4C,CAAC,CAAC;SACnE;QACD;;;;QAIE,IAAI,KAAK,WAAW;YACpB,IAAI,KAAK,aAAa;YACtB,IAAI,KAAK,WAAW,EACpB;YACA,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;SAC9D;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACrB,IAAI,IAAI,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,EAAE;gBAClC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACb,OAAO,SAAS,CAAC;aAClB;YACD,CAAC,GAAG,qBAAqB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YACpC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjB,IAAI,MAAM,KAAK,GAAG,EAAE;oBAClB,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;oBACZ,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;oBACZ,MAAM;iBACP;gBACD,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;aACZ;iBAAM,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE;gBAC5C,IAAI,KAAK,EAAE;oBACT,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACb,OAAO,SAAS,CAAC;iBAClB;aACF;iBAAM,IAAI,KAAK,EAAE;gBAChB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;aACxC;SACF;aAAM;YACL,IAAI,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;gBACnC,IAAI,KAAK,EAAE;oBACT,IAAI,MAAM,KAAK,GAAG,EAAE;wBAClB,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;wBACf,OAAO,SAAS,CAAC;qBAClB;;oBAED,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;oBACnC,IACE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;wBACnB,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EACzC;wBACA,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;wBACnB,SAAS;qBACV;oBACD,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;oBACnB,SAAS;iBACV;gBACD,OAAO,SAAS,CAAC;aAClB;YACD,IAAI,MAAM,KAAK,GAAG,EAAE;gBAClB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;gBACf,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;gBACf,MAAM;aACP;YACD,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;SACf;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;SAEe,gBAAgB,CAAC,MAAe,EAAE,IAAkB;IAClE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;KACtE;IACD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACjC,MAAM,IAAI,SAAS,CAAC,kCAAkC,CAAC,CAAC;KACzD;;IAED,IAAI,EAAE,GAAQ,MAAM,CAAC;IACrB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;IACxB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5B,IAAI,IAAiB,CAAC;IACtB,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;IAChB,IAAI,GAAY,CAAC;IACjB,IAAI,CAAS,CAAC;IACd,OAAO,EAAE,MAAM,GAAG,GAAG,EAAE;QACrB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YACxD,MAAM,IAAI,SAAS,CAAC,4CAA4C,CAAC,CAAC;SACnE;QACD,IACE,IAAI,KAAK,WAAW;YACpB,IAAI,KAAK,aAAa;YACtB,IAAI,KAAK,WAAW,EACpB;YACA,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;SAC9D;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACrB,CAAC,GAAG,qBAAqB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YACpC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM;gBAAE,OAAO,SAAS,CAAC;YACrC,IAAI,MAAM,KAAK,GAAG,EAAE;gBAClB,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACZ,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACb,MAAM;aACP;YACD,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;SACZ;aAAM;YACL,IAAI,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;gBACnC,OAAO,SAAS,CAAC;aAClB;YACD,IAAI,MAAM,KAAK,GAAG,EAAE;gBAClB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;gBACf,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;gBAChB,MAAM;aACP;YACD,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;SACf;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;SAEe,iBAAiB,CAAC,GAAY;IAC5C,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;AACrE,CAAC;SAEe,WAAW,CAAC,GAAY;IACtC,OAAO,iBAAiB,CAAC,GAAG,CAAC,GAAG,2BAA2B,GAAG,aAAa,CAAC;AAC9E,CAAC;SAEe,aAAa,CAAC,GAA2B;IACvD,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;UACrB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;UACZ,WAAW,CAAC,GAAc,CAAC,CAAC,GAAc,CAAC,CAAC;AAClD;;AClWA;;;;;;;AAOA,SAAS,QAAQ,CAAC,KAAc;IAC9B,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC;AACrD,CAAC;AAeD;AACA,SAAS,aAAa,CAAC,GAAY;IACjC,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC1D,CAAC;AACD;AACA,SAAS,eAAe,CACtB,MAAe,EACf,OAAgB,EAChB,OAAgB;IAEhB,MAAM,eAAe,GAAG,IAAI,GAAG,EAAwB,CAAC;IACxD,MAAM,CAAC,GAAW,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9C,OAAO,CAAC,CAAC,MAAM,EAAE;QACf,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,EAAU,CAAC;QACxC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5B,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE;YACtB,eAAe,CAAC,GAAG,CACjB,GAAG,EACH,IAAI,WAAW,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC,CACnD,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACvB,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAA8B,CAAC,CAAC;gBACzD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;gBACxB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;gBACX,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE;oBAChB,MAAM,EAAE,GAAI,GAA+B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrD,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;wBAC3C,CAAC,CAAC,IAAI,CAAC;4BACL,GAAG,EAAE,IAAI,aAAa,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAgB,CAAC;4BAC9D,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBAC3B,CAAC,CAAC;qBACJ;yBAAM;wBACL,CAAC,CAAC,IAAI,CAAC;4BACL,GAAG,EAAE,EAAE;4BACP,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBAC3B,CAAC,CAAC;qBACJ;iBACF;aACF;iBAAM;;gBAEL,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;gBACX,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;gBACvB,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE;oBAChB,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBAClB,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;wBAC3C,CAAC,CAAC,IAAI,CAAC;4BACL,GAAG,EAAE,IAAI,aAAa,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAgB,CAAC;4BAC9D,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;yBAC5B,CAAC,CAAC;qBACJ;yBAAM;wBACL,CAAC,CAAC,IAAI,CAAC;4BACL,GAAG,EAAE,EAAE;4BACP,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;yBAC5B,CAAC,CAAC;qBACJ;iBACF;aACF;SACF;KACF;AACH,CAAC;AAED;AACA,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAC/B;AACA,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;AAClC;AACA,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA2Ga,WAAW;;;;;IA8QtB,YAAY,GAA2B;QACrC,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;KAChC;;;;;;;;;;;;;IA5PD,OAAO,MAAM,CAAC,OAA+B;QAC3C,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;KACjC;;;;;;;;;;;;;;;;;;;;IAqBD,OAAO,GAAG,CACR,MAAe,EACf,OAA6C;QAE7C,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzD,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;SACpC;QACD,OAAQ,OAAuB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;KAC7C;;;;;;;;;;;;;;;;;;;;IAqBD,OAAO,GAAG,CACR,MAAe,EACf,OAA6C;QAE7C,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzD,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;SACpC;QACD,OAAQ,OAAuB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;KAC7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsCD,OAAO,GAAG,CACR,MAAe,EACf,OAA6C,EAC7C,GAAY,EACZ,KAAK,GAAG,KAAK;QAEb,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzD,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;SACpC;QACD,OAAQ,OAAuB,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;KACzD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgCD,OAAO,KAAK,CACV,MAAe,EACf,OAA6C;QAE7C,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzD,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;SACpC;QACD,OAAQ,OAAuB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KAC/C;;;;;IAMD,OAAO,MAAM,CAAC,OAAgB;QAC5B,OAAO,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;KACtC;;;;;;;IAQD,OAAO,KAAK,CAAC,MAAe,EAAE,OAAgB,EAAE,UAAU,GAAG,KAAK;QAChE,eAAe,CACb,MAAM,EACN,OAAO,EACP,UAAU,GAAG,2BAA2B,GAAG,aAAa,CACzD,CAAC;KACH;;;;;IAMD,OAAO,YAAY,CAAC,MAAe;QACjC,MAAM,GAAG,GAAgC,EAAE,CAAC;QAC5C,eAAe,CACb,MAAM,EACN,CAAC,OAAO,EAAE,KAAK;YACb,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;SAC9B,EACD,aAAa,CACd,CAAC;QACF,OAAO,GAAG,CAAC;KACZ;;;;;IAMD,OAAO,eAAe,CACpB,MAAe;QAEf,MAAM,GAAG,GAA2C,EAAE,CAAC;QACvD,eAAe,CACb,MAAM,EACN,CAAC,UAAU,EAAE,KAAK;YAChB,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;SACjC,EACD,2BAA2B,CAC5B,CAAC;QACF,OAAO,GAAG,CAAC;KACZ;;;;;;IAOD,OAAO,OAAO,CACZ,MAAe,EACf,UAAU,GAAG,KAAK;QAElB,MAAM,GAAG,GAA6B,EAAE,CAAC;QACzC,eAAe,CACb,MAAM,EACN,CAAC,CAAC,EAAE,CAAC;YACH,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACZ,EACD,UAAU,GAAG,2BAA2B,GAAG,aAAa,CACzD,CAAC;QACF,OAAO,GAAG,CAAC;KACZ;;;;;;IAOD,OAAO,GAAG,CAAC,MAAe,EAAE,UAAU,GAAG,KAAK;QAC5C,MAAM,GAAG,GAAG,IAAI,GAAG,EAAoB,CAAC;QACxC,eAAe,CACb,MAAM,EACN,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EACjB,UAAU,GAAG,2BAA2B,GAAG,aAAa,CACzD,CAAC;QACF,OAAO,GAAG,CAAC;KACZ;;;;;IAmBD,GAAG,CAAC,MAAe;QACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACf,IAAI,CAAC,IAAI,CAAC,GAAG,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACnD;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;KAC3B;;;;;;;;;;;;IAaD,GAAG,CAAC,MAAe,EAAE,KAAc,EAAE,KAAK,GAAG,KAAK;QAChD,OAAO,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACxD;;;;;;;IAQD,KAAK,CAAC,MAAe;QACnB,OAAO,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;KAC5C;;;;;IAMD,GAAG,CAAC,MAAe;QACjB,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,WAAW,CAAC;KAChD;;;;;IAMD,MAAM,CAAC,MAAe;QACpB,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QACpB,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC;YAAE,OAAO,SAAS,CAAC;QACpC,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACzD,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;KAC3B;;;;;;IAOD,QAAQ,CAAC,GAAwB;QAC/B,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QACpB,MAAM,OAAO,GAAG,qBAAqB,CAAC,GAAG,CAAa,CAAC;QACvD,MAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvE,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;;YAE5C,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC;YACvC,MAAM,IAAI,KAAK,CACb,uDAAuD,IAAI,8CAA8C,CAC1G,CAAC;SACH;QACD,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;KAC3B;;;;;;;IAQD,GAAG,CAAC,MAAe,EAAE,GAAwB;QAC3C,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QACpB,MAAM,OAAO,GAAG,qBAAqB,CAAC,GAAG,CAAa,CAAC;QACvD,MAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE;;YAEhB,OAAO,SAAS,CAAC;SAClB;QACD,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;;YAE5C,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC;YACvC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACpC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;SAC1D;QACD,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;KAC1B;;;;;IAMD,MAAM,CAAC,GAAyC;QAC9C,OAAO,IAAI,WAAW,CACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CACd,GAAG,YAAY,WAAW,GAAG,GAAG,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,CAC3D,CACF,CAAC;KACH;;;;IAKD,IAAI,OAAO;QACT,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACvC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;KACnB;;;;IAKD,IAAI,qBAAqB;QACvB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACf,IAAI,CAAC,IAAI,CAAC,GAAG,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrD;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;KACnB;;;;IAKD,QAAQ;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;CACF;AAED;AACA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAEnC;;;;;;MAMa,aAAa;;;;;IAuBxB,YAAY,OAA6C;QACvD,IAAI,CAAC,QAAQ,CAAC;YACZ,OAAO,YAAY,WAAW,GAAG,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QACtE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,qBAAqB,CAAC;KAClD;;;;;IAtBD,OAAO,WAAW,CAAC,SAAkB;QACnC,IAAI,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC;QAC7B,MAAM,GAAG,GAAG,SAAqC,CAAC;QAClD,OAAO,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,UAAU,CAAC;KAC1E;;;;;;IAyBD,OAAO,CAAC,MAAe;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;KACnC;;;;IAKD,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC;KACvB;;;;IAKD,QAAQ;QACN,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;;;;;;;;;;;;;;;;;;;;;;;"}