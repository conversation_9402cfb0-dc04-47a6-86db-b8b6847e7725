#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_DEFAULT_LINKOPTS",
    "ABSL_TEST_COPTS",
)

package(
    default_visibility = ["//visibility:private"],
    features = [
        "header_modules",
        "layering_check",
        "parse_headers",
    ],
)

licenses(["notice"])

# Internal data structure for efficiently detecting mutex dependency cycles
cc_library(
    name = "graphcycles_internal",
    srcs = [
        "internal/graphcycles.cc",
    ],
    hdrs = [
        "internal/graphcycles.h",
    ],
    copts = ABSL_DEFAULT_COPTS + select({
        "//conditions:default": [],
    }),
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base",
        "//absl/base:base_internal",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:malloc_internal",
        "//absl/base:raw_logging_internal",
    ],
)

cc_library(
    name = "kernel_timeout_internal",
    srcs = ["internal/kernel_timeout.cc"],
    hdrs = ["internal/kernel_timeout.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
    ],
    deps = [
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
        "//absl/time",
    ] + select({
        "//conditions:default": [],
    }),
)

cc_test(
    name = "kernel_timeout_internal_test",
    srcs = ["internal/kernel_timeout_test.cc"],
    copts = ABSL_TEST_COPTS,
    flaky = 1,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":kernel_timeout_internal",
        "//absl/base:config",
        "//absl/random",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "synchronization",
    srcs = [
        "barrier.cc",
        "blocking_counter.cc",
        "internal/create_thread_identity.cc",
        "internal/futex_waiter.cc",
        "internal/per_thread_sem.cc",
        "internal/pthread_waiter.cc",
        "internal/sem_waiter.cc",
        "internal/stdcpp_waiter.cc",
        "internal/waiter_base.cc",
        "internal/win32_waiter.cc",
        "mutex.cc",
        "notification.cc",
    ],
    hdrs = [
        "barrier.h",
        "blocking_counter.h",
        "internal/create_thread_identity.h",
        "internal/futex.h",
        "internal/futex_waiter.h",
        "internal/per_thread_sem.h",
        "internal/pthread_waiter.h",
        "internal/sem_waiter.h",
        "internal/stdcpp_waiter.h",
        "internal/waiter.h",
        "internal/waiter_base.h",
        "internal/win32_waiter.h",
        "mutex.h",
        "notification.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = select({
        "@rules_cc//cc/compiler:msvc-cl": [],
        "@rules_cc//cc/compiler:clang-cl": [],
        "@rules_cc//cc/compiler:emscripten": [],
        "//conditions:default": ["-pthread"],
    }) + ABSL_DEFAULT_LINKOPTS,
    visibility = ["//visibility:public"],
    deps = [
        ":graphcycles_internal",
        ":kernel_timeout_internal",
        "//absl/base",
        "//absl/base:atomic_hook",
        "//absl/base:base_internal",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "//absl/base:malloc_internal",
        "//absl/base:raw_logging_internal",
        "//absl/base:tracing_internal",
        "//absl/debugging:stacktrace",
        "//absl/debugging:symbolize",
        "//absl/time",
    ] + select({
        "//conditions:default": [],
    }),
)

cc_test(
    name = "barrier_test",
    size = "small",
    srcs = ["barrier_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test_wasm",  # b/122473323
    ],
    deps = [
        ":synchronization",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "blocking_counter_test",
    size = "small",
    srcs = ["blocking_counter_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test_wasm",  # b/122473323
    ],
    deps = [
        ":synchronization",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:tracing_internal",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_binary(
    name = "blocking_counter_benchmark",
    testonly = True,
    srcs = ["blocking_counter_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    deps = [
        ":synchronization",
        ":thread_pool",
        "//absl/base:no_destructor",
        "@google_benchmark//:benchmark_main",
    ],
)

cc_test(
    name = "graphcycles_test",
    size = "medium",
    srcs = ["internal/graphcycles_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":graphcycles_internal",
        "//absl/base:core_headers",
        "//absl/log",
        "//absl/log:check",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "graphcycles_benchmark",
    srcs = ["internal/graphcycles_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "benchmark",
    ],
    deps = [
        ":graphcycles_internal",
        "//absl/base:raw_logging_internal",
        "@google_benchmark//:benchmark_main",
        "@googletest//:gtest",
    ],
)

cc_library(
    name = "thread_pool",
    testonly = True,
    hdrs = ["internal/thread_pool.h"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//absl:__subpackages__",
    ],
    deps = [
        ":synchronization",
        "//absl/base:core_headers",
        "//absl/functional:any_invocable",
    ],
)

cc_test(
    name = "mutex_test",
    size = "large",
    srcs = ["mutex_test.cc"],
    copts = ABSL_TEST_COPTS,
    flaky = 1,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    shard_count = 25,
    deps = [
        ":synchronization",
        ":thread_pool",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/log",
        "//absl/log:check",
        "//absl/memory",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "mutex_method_pointer_test",
    srcs = ["mutex_method_pointer_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":synchronization",
        "//absl/base:config",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "mutex_benchmark_common",
    testonly = True,
    srcs = ["mutex_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
    ],
    deps = [
        ":synchronization",
        ":thread_pool",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:no_destructor",
        "@google_benchmark//:benchmark_main",
    ],
    alwayslink = 1,
)

cc_binary(
    name = "mutex_benchmark",
    testonly = True,
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":mutex_benchmark_common",
    ],
)

cc_test(
    name = "notification_test",
    size = "small",
    srcs = ["notification_test.cc"],
    copts = ABSL_TEST_COPTS,
    flaky = 1,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["no_test_lexan"],
    deps = [
        ":synchronization",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:tracing_internal",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "per_thread_sem_test_common",
    testonly = True,
    srcs = ["internal/per_thread_sem_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
    ],
    deps = [
        ":synchronization",
        "//absl/base",
        "//absl/base:config",
        "//absl/strings",
        "//absl/time",
        "@googletest//:gtest",
    ],
    alwayslink = 1,
)

cc_test(
    name = "per_thread_sem_test",
    size = "large",
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test_wasm",
    ],
    deps = [
        ":per_thread_sem_test_common",
        ":synchronization",
        "//absl/strings",
        "//absl/time",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "waiter_test",
    srcs = ["internal/waiter_test.cc"],
    copts = ABSL_TEST_COPTS,
    flaky = 1,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":kernel_timeout_internal",
        ":synchronization",
        ":thread_pool",
        "//absl/base:config",
        "//absl/random",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "lifetime_test",
    srcs = [
        "lifetime_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test_ios_x86_64",
        "no_test_wasm",
    ],
    deps = [
        ":synchronization",
        "//absl/base:core_headers",
        "//absl/log:check",
    ],
)
