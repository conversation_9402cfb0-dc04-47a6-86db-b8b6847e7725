#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This package contains `absl::Status`.
# It will expand later to have utilities around `Status` like `StatusOr`,
# `StatusBuilder` and macros.

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_DEFAULT_LINKOPTS",
    "ABSL_TEST_COPTS",
)

package(
    default_visibility = ["//visibility:public"],
    features = [
        "header_modules",
        "layering_check",
        "parse_headers",
    ],
)

licenses(["notice"])

cc_library(
    name = "status",
    srcs = [
        "internal/status_internal.cc",
        "internal/status_internal.h",
        "status.cc",
        "status_payload_printer.cc",
    ],
    hdrs = [
        "status.h",
        "status_payload_printer.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:atomic_hook",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:no_destructor",
        "//absl/base:nullability",
        "//absl/base:raw_logging_internal",
        "//absl/base:strerror",
        "//absl/container:inlined_vector",
        "//absl/debugging:leak_check",
        "//absl/debugging:stacktrace",
        "//absl/debugging:symbolize",
        "//absl/functional:function_ref",
        "//absl/memory",
        "//absl/strings",
        "//absl/strings:cord",
        "//absl/strings:str_format",
        "//absl/types:optional",
        "//absl/types:span",
    ],
)

cc_test(
    name = "status_test",
    srcs = ["status_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":status",
        "//absl/strings",
        "//absl/strings:cord",
        "//absl/strings:str_format",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "statusor",
    srcs = [
        "internal/statusor_internal.h",
        "statusor.cc",
    ],
    hdrs = [
        "statusor.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":status",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:nullability",
        "//absl/base:raw_logging_internal",
        "//absl/meta:type_traits",
        "//absl/strings",
        "//absl/strings:has_ostream_operator",
        "//absl/strings:str_format",
        "//absl/types:variant",
        "//absl/utility",
    ],
)

cc_test(
    name = "statusor_test",
    size = "small",
    srcs = ["statusor_test.cc"],
    deps = [
        ":status",
        ":status_matchers",
        ":statusor",
        "//absl/base",
        "//absl/memory",
        "//absl/strings",
        "//absl/types:any",
        "//absl/types:variant",
        "//absl/utility",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "status_matchers",
    testonly = 1,
    srcs = [
        "internal/status_matchers.cc",
        "internal/status_matchers.h",
    ],
    hdrs = ["status_matchers.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":status",
        ":statusor",
        "//absl/base:config",
        "//absl/strings:string_view",
        "@googletest//:gtest",
    ],
)

cc_test(
    name = "status_matchers_test",
    size = "small",
    srcs = ["status_matchers_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":status",
        ":status_matchers",
        ":statusor",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)
