{"compilerOptions": {"declaration": true, "target": "ES2020", "module": "ESNext", "moduleResolution": "Node16", "experimentalDecorators": true, "noImplicitAny": false, "removeComments": true, "preserveConstEnums": true, "noEmitHelpers": true, "skipLibCheck": true, "outDir": "dist/mjs", "rootDir": "src", "types": ["vitest/globals", "node"]}, "compileOnSave": true, "buildOnSave": true, "exclude": ["**/*.spec.ts", "bin", "dist", "vite.config.ts", "node_modules/**"]}