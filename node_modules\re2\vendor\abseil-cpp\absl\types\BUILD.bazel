#
# Copyright 2017 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_DEFAULT_LINKOPTS",
    "ABSL_TEST_COPTS",
)

package(
    default_visibility = ["//visibility:public"],
    features = [
        "header_modules",
        "layering_check",
        "parse_headers",
    ],
)

licenses(["notice"])

cc_library(
    name = "any",
    hdrs = ["any.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":bad_any_cast",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:fast_type_id",
        "//absl/meta:type_traits",
        "//absl/utility",
    ],
)

cc_library(
    name = "bad_any_cast",
    hdrs = ["bad_any_cast.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":bad_any_cast_impl",
        "//absl/base:config",
    ],
)

cc_library(
    name = "bad_any_cast_impl",
    srcs = [
        "bad_any_cast.cc",
        "bad_any_cast.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = ["//visibility:private"],
    deps = [
        "//absl/base:config",
        "//absl/base:raw_logging_internal",
    ],
)

cc_test(
    name = "any_test",
    size = "small",
    srcs = [
        "any_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":any",
        "//absl/base:config",
        "//absl/base:exception_testing",
        "//absl/container:test_instance_tracker",
        "//absl/log",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "any_exception_safety_test",
    srcs = ["any_exception_safety_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":any",
        "//absl/base:config",
        "//absl/base:exception_safety_testing",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "span",
    srcs = [
        "internal/span.h",
    ],
    hdrs = [
        "span.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/algorithm",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:nullability",
        "//absl/base:throw_delegate",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "span_test",
    size = "small",
    srcs = ["span_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":span",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:exception_testing",
        "//absl/container:fixed_array",
        "//absl/container:inlined_vector",
        "//absl/hash:hash_testing",
        "//absl/meta:type_traits",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "optional",
    srcs = ["internal/optional.h"],
    hdrs = ["optional.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":bad_optional_access",
        "//absl/base:base_internal",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:nullability",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/utility",
    ],
)

cc_library(
    name = "bad_optional_access",
    srcs = ["bad_optional_access.cc"],
    hdrs = ["bad_optional_access.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:raw_logging_internal",
    ],
)

cc_library(
    name = "bad_variant_access",
    srcs = ["bad_variant_access.cc"],
    hdrs = ["bad_variant_access.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:raw_logging_internal",
    ],
)

cc_test(
    name = "optional_test",
    size = "small",
    srcs = [
        "optional_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":optional",
        "//absl/base:config",
        "//absl/log",
        "//absl/meta:type_traits",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "optional_exception_safety_test",
    srcs = [
        "optional_exception_safety_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":optional",
        "//absl/base:config",
        "//absl/base:exception_safety_testing",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "variant",
    srcs = ["internal/variant.h"],
    hdrs = ["variant.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":bad_variant_access",
        "//absl/base:base_internal",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/meta:type_traits",
        "//absl/utility",
    ],
)

cc_test(
    name = "variant_test",
    size = "small",
    srcs = ["variant_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":variant",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/memory",
        "//absl/meta:type_traits",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "variant_benchmark",
    srcs = [
        "variant_benchmark.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    deps = [
        ":variant",
        "//absl/utility",
        "@google_benchmark//:benchmark_main",
        "@googletest//:gtest",
    ],
)

cc_test(
    name = "variant_exception_safety_test",
    size = "small",
    srcs = [
        "variant_exception_safety_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":variant",
        "//absl/base:config",
        "//absl/base:exception_safety_testing",
        "//absl/memory",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "compare",
    hdrs = ["compare.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "compare_test",
    size = "small",
    srcs = [
        "compare_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":compare",
        "//absl/base",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)
