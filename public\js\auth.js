// Authentication JavaScript for PDF to Exam Converter

document.addEventListener('DOMContentLoaded', function() {
    initializeAuth();
});

function initializeAuth() {
    // Setup tab navigation
    setupTabNavigation();
    
    // Setup form handlers
    setupFormHandlers();
    
    // Setup password toggles
    setupPasswordToggles();
    
    // Setup Google authentication
    setupGoogleAuth();
    
    // Check URL parameters for default tab
    checkURLParameters();
}

// Setup tab navigation
function setupTabNavigation() {
    const loginTab = document.getElementById('loginTab');
    const signupTab = document.getElementById('signupTab');
    const loginForm = document.getElementById('loginForm');
    const signupForm = document.getElementById('signupForm');
    
    loginTab.addEventListener('click', function() {
        showLoginForm();
    });
    
    signupTab.addEventListener('click', function() {
        showSignupForm();
    });
    
    function showLoginForm() {
        loginTab.classList.add('bg-primary-600', 'text-white');
        loginTab.classList.remove('text-gray-600');
        signupTab.classList.remove('bg-primary-600', 'text-white');
        signupTab.classList.add('text-gray-600');
        
        loginForm.classList.remove('hidden');
        signupForm.classList.add('hidden');
        
        // Update URL
        const url = new URL(window.location);
        url.searchParams.set('type', 'login');
        window.history.replaceState({}, '', url);
    }
    
    function showSignupForm() {
        signupTab.classList.add('bg-primary-600', 'text-white');
        signupTab.classList.remove('text-gray-600');
        loginTab.classList.remove('bg-primary-600', 'text-white');
        loginTab.classList.add('text-gray-600');
        
        signupForm.classList.remove('hidden');
        loginForm.classList.add('hidden');
        
        // Update URL
        const url = new URL(window.location);
        url.searchParams.set('type', 'signup');
        window.history.replaceState({}, '', url);
    }
    
    // Set default active tab
    showLoginForm();
}

// Setup form handlers
function setupFormHandlers() {
    const loginFormElement = document.getElementById('loginFormElement');
    const signupFormElement = document.getElementById('signupFormElement');
    
    // Login form
    loginFormElement.addEventListener('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });
    
    // Signup form
    signupFormElement.addEventListener('submit', function(e) {
        e.preventDefault();
        handleSignup();
    });
    
    // Forgot password
    const forgotPassword = document.getElementById('forgotPassword');
    forgotPassword.addEventListener('click', function(e) {
        e.preventDefault();
        handleForgotPassword();
    });
}

// Setup password toggles
function setupPasswordToggles() {
    const toggleButtons = [
        'toggleLoginPassword',
        'toggleSignupPassword',
        'toggleConfirmPassword'
    ];
    
    toggleButtons.forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button) {
            button.addEventListener('click', function() {
                const input = this.parentElement.querySelector('input');
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }
    });
}

// Setup Google authentication
function setupGoogleAuth() {
    const googleSignInBtn = document.getElementById('googleSignInBtn');
    const googleSignUpBtn = document.getElementById('googleSignUpBtn');
    
    if (googleSignInBtn) {
        googleSignInBtn.addEventListener('click', handleGoogleAuth);
    }
    
    if (googleSignUpBtn) {
        googleSignUpBtn.addEventListener('click', handleGoogleAuth);
    }
}

// Check URL parameters
function checkURLParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const type = urlParams.get('type');
    
    if (type === 'signup') {
        document.getElementById('signupTab').click();
    }
}

// Handle login
async function handleLogin() {
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    // Validate inputs
    if (!isValidEmail(email)) {
        showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
        return;
    }
    
    if (password.length < 6) {
        showNotification('كلمة المرور قصيرة جداً', 'error');
        return;
    }
    
    showLoading(true);
    
    try {
        // Simulate login (replace with Firebase Auth)
        await simulateAuth(email, password, 'login');
        
        const userData = {
            email: email,
            name: email.split('@')[0],
            loginTime: new Date().toISOString(),
            rememberMe: rememberMe
        };
        
        // Store user data
        if (rememberMe) {
            localStorage.setItem('user', JSON.stringify(userData));
        } else {
            sessionStorage.setItem('user', JSON.stringify(userData));
        }
        
        showNotification('تم تسجيل الدخول بنجاح!', 'success');
        
        // Redirect to dashboard or upload page
        setTimeout(() => {
            const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 'dashboard.html';
            window.location.href = redirectUrl;
        }, 1000);
        
    } catch (error) {
        showNotification('خطأ في تسجيل الدخول: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// Handle signup
async function handleSignup() {
    const name = document.getElementById('signupName').value;
    const email = document.getElementById('signupEmail').value;
    const password = document.getElementById('signupPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const agreeTerms = document.getElementById('agreeTerms').checked;
    
    // Validate inputs
    if (name.trim().length < 2) {
        showNotification('يرجى إدخال اسم صحيح', 'error');
        return;
    }
    
    if (!isValidEmail(email)) {
        showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
        return;
    }
    
    if (password.length < 8) {
        showNotification('كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        showNotification('كلمات المرور غير متطابقة', 'error');
        return;
    }
    
    if (!agreeTerms) {
        showNotification('يجب الموافقة على شروط الاستخدام', 'error');
        return;
    }
    
    showLoading(true);
    
    try {
        // Simulate signup (replace with Firebase Auth)
        await simulateAuth(email, password, 'signup');
        
        const userData = {
            name: name,
            email: email,
            signupTime: new Date().toISOString()
        };
        
        // Store user data
        localStorage.setItem('user', JSON.stringify(userData));
        
        showNotification('تم إنشاء الحساب بنجاح!', 'success');
        
        // Redirect to dashboard or upload page
        setTimeout(() => {
            const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 'upload.html';
            window.location.href = redirectUrl;
        }, 1000);
        
    } catch (error) {
        showNotification('خطأ في إنشاء الحساب: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// Handle Google authentication
async function handleGoogleAuth() {
    showLoading(true);
    
    try {
        // Simulate Google auth (replace with Firebase Auth)
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const userData = {
            name: 'مستخدم Google',
            email: '<EMAIL>',
            provider: 'google',
            loginTime: new Date().toISOString()
        };
        
        localStorage.setItem('user', JSON.stringify(userData));
        
        showNotification('تم تسجيل الدخول بـ Google بنجاح!', 'success');
        
        setTimeout(() => {
            const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 'dashboard.html';
            window.location.href = redirectUrl;
        }, 1000);
        
    } catch (error) {
        showNotification('خطأ في تسجيل الدخول بـ Google: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// Handle forgot password
function handleForgotPassword() {
    const email = document.getElementById('loginEmail').value;
    
    if (!email) {
        showNotification('يرجى إدخال بريدك الإلكتروني أولاً', 'warning');
        return;
    }
    
    if (!isValidEmail(email)) {
        showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
        return;
    }
    
    showLoading(true);
    
    // Simulate password reset
    setTimeout(() => {
        showLoading(false);
        showNotification('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني', 'success');
    }, 2000);
}

// Simulate authentication (replace with actual Firebase implementation)
function simulateAuth(email, password, type) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            // Simulate some validation
            if (email === '<EMAIL>' && password === 'wrongpassword') {
                reject(new Error('كلمة المرور غير صحيحة'));
            } else if (type === 'signup' && email === '<EMAIL>') {
                reject(new Error('هذا البريد الإلكتروني مستخدم بالفعل'));
            } else {
                resolve({ email, type });
            }
        }, 1500);
    });
}

// Show/hide loading overlay
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (show) {
        overlay.classList.remove('hidden');
    } else {
        overlay.classList.add('hidden');
    }
}

// Password strength checker
function checkPasswordStrength(password) {
    let strength = 0;
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    return strength;
}

// Real-time password validation
document.addEventListener('DOMContentLoaded', function() {
    const signupPassword = document.getElementById('signupPassword');
    if (signupPassword) {
        signupPassword.addEventListener('input', function() {
            const strength = checkPasswordStrength(this.value);
            // You can add visual feedback here
        });
    }
});
