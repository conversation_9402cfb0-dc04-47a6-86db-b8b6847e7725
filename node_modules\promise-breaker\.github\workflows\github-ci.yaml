name: GitHub CI
on: [push]

jobs:
    test:
        runs-on: ubuntu-latest
        strategy:
            matrix:
                node-versions: [12.x, 14.x, 16.x]
        steps:
            - uses: actions/checkout@v2
            - name: Setup Node ${{ matrix.node-version }}
              uses: actions/setup-node@v1
              with:
                  node-version: ${{ matrix.node-version }}
            - run: npm install
            - name: test
              run: npm test
            - name: Coveralls
              uses: coverallsapp/github-action@master
              with:
                  github-token: ${{ secrets.GITHUB_TOKEN }}
    release:
        runs-on: ubuntu-latest
        needs: test
        if: github.ref == 'refs/heads/master'
        steps:
            - uses: actions/checkout@v2
            - name: Setup Node ${{ matrix.node-version }}
              uses: actions/setup-node@v1
              with:
                  node-version: 14
            - run: npm install
            - name: semantic-release
              run: npm run semantic-release
              env:
                  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
                  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
