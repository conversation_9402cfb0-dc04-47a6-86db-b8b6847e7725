<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع ملف PDF - محول PDF إلى امتحانات</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Supabase -->
    <script type="module" src="js/supabase-config.js"></script>
</head>
<body class="font-arabic bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-primary-600">
                            <i class="fas fa-file-pdf mr-2"></i>
                            محول PDF
                        </h1>
                    </a>
                </div>
                
                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-4 space-x-reverse">
                    <a href="dashboard.html" class="text-gray-500 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-tachometer-alt mr-1"></i>
                        لوحة التحكم
                    </a>
                    <a href="upload.html" class="text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-upload mr-1"></i>
                        رفع ملف
                    </a>
                </div>
                
                <!-- User Menu -->
                <div class="flex items-center">
                    <div class="relative">
                        <button id="userMenuBtn" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <img id="userAvatar" class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=User&background=3b82f6&color=fff" alt="User">
                            <span id="userName" class="mr-2 text-gray-700">مستخدم</span>
                            <i class="fas fa-chevron-down mr-1 text-gray-400"></i>
                        </button>
                        
                        <!-- Dropdown menu -->
                        <div id="userDropdown" class="hidden absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                            <div class="py-1">
                                <a href="dashboard.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-tachometer-alt mr-2"></i>
                                    لوحة التحكم
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i>
                                    الملف الشخصي
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-cog mr-2"></i>
                                    الإعدادات
                                </a>
                                <div class="border-t border-gray-100"></div>
                                <a href="#" id="logoutBtn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>
                                    تسجيل الخروج
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">
                <i class="fas fa-cloud-upload-alt text-primary-600 mr-2"></i>
                رفع ملف PDF
            </h1>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                ارفع ملف PDF الخاص بك وسنقوم بتحويله إلى امتحان تفاعلي باستخدام الذكاء الاصطناعي
            </p>
        </div>

        <!-- Upload Form -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <!-- Step Indicator -->
            <div class="mb-8">
                <div class="flex items-center justify-center">
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-8 h-8 bg-primary-600 rounded-full text-white text-sm font-medium">
                            1
                        </div>
                        <span class="mr-2 text-sm font-medium text-primary-600">رفع الملف</span>
                    </div>
                    <div class="flex-1 h-0.5 bg-gray-200 mx-4"></div>
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-8 h-8 bg-gray-200 rounded-full text-gray-500 text-sm font-medium">
                            2
                        </div>
                        <span class="mr-2 text-sm font-medium text-gray-500">التحليل</span>
                    </div>
                    <div class="flex-1 h-0.5 bg-gray-200 mx-4"></div>
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-8 h-8 bg-gray-200 rounded-full text-gray-500 text-sm font-medium">
                            3
                        </div>
                        <span class="mr-2 text-sm font-medium text-gray-500">النتيجة</span>
                    </div>
                </div>
            </div>

            <!-- File Upload Area -->
            <div id="uploadArea" class="drag-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center mb-6 transition-all duration-300 hover:border-primary-400 hover:bg-primary-50">
                <div id="uploadContent">
                    <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">اسحب وأفلت ملف PDF هنا</h3>
                    <p class="text-gray-500 mb-4">أو</p>
                    <button id="selectFileBtn" class="btn-primary">
                        <i class="fas fa-folder-open mr-2"></i>
                        اختر ملف من جهازك
                    </button>
                    <input type="file" id="fileInput" accept=".pdf" class="hidden">
                    <p class="text-sm text-gray-400 mt-4">
                        الحد الأقصى لحجم الملف: 10 ميجابايت | الصيغ المدعومة: PDF
                    </p>
                </div>
                
                <!-- File Preview -->
                <div id="filePreview" class="hidden">
                    <div class="flex items-center justify-center mb-4">
                        <i class="fas fa-file-pdf text-red-500 text-4xl mr-3"></i>
                        <div class="text-right">
                            <p id="fileName" class="font-semibold text-gray-900"></p>
                            <p id="fileSize" class="text-sm text-gray-500"></p>
                        </div>
                    </div>
                    <div class="flex justify-center space-x-2 space-x-reverse">
                        <button id="removeFileBtn" class="text-red-600 hover:text-red-700 text-sm">
                            <i class="fas fa-trash mr-1"></i>
                            إزالة الملف
                        </button>
                        <button id="changeFileBtn" class="text-primary-600 hover:text-primary-700 text-sm">
                            <i class="fas fa-edit mr-1"></i>
                            تغيير الملف
                        </button>
                    </div>
                </div>
            </div>

            <!-- Configuration Options -->
            <div id="configOptions" class="hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">خيارات التخصيص</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Language Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-language mr-1"></i>
                            لغة المحتوى
                        </label>
                        <select id="contentLanguage" class="input-field">
                            <option value="ar">العربية</option>
                            <option value="en">الإنجليزية</option>
                            <option value="auto">تحديد تلقائي</option>
                        </select>
                    </div>
                    
                    <!-- Question Types -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-question-circle mr-1"></i>
                            أنواع الأسئلة
                        </label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" id="mcqQuestions" checked class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                <span class="mr-2 text-sm">اختيار من متعدد (MCQ)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="trueFalseQuestions" checked class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                <span class="mr-2 text-sm">صح وخطأ</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="fillBlankQuestions" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                <span class="mr-2 text-sm">املأ الفراغ</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Number of Questions -->
                    <div>
                        <label for="questionCount" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-hashtag mr-1"></i>
                            عدد الأسئلة المطلوبة
                        </label>
                        <input type="number" id="questionCount" min="5" max="50" value="20" class="input-field">
                        <p class="text-xs text-gray-500 mt-1">بين 5 و 50 سؤال</p>
                    </div>
                    
                    <!-- Difficulty Level -->
                    <div>
                        <label for="difficultyLevel" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-chart-line mr-1"></i>
                            مستوى الصعوبة
                        </label>
                        <select id="difficultyLevel" class="input-field">
                            <option value="easy">سهل</option>
                            <option value="medium" selected>متوسط</option>
                            <option value="hard">صعب</option>
                            <option value="mixed">مختلط</option>
                        </select>
                    </div>
                </div>
                
                <!-- Additional Options -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-cogs mr-1"></i>
                        خيارات إضافية
                    </label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" id="includeExplanations" checked class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm">تضمين شرح للإجابات الصحيحة</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="randomizeQuestions" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm">ترتيب عشوائي للأسئلة</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="timeLimit" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm">تحديد وقت للامتحان</span>
                        </label>
                    </div>
                </div>
                
                <!-- Time Limit Input -->
                <div id="timeLimitInput" class="hidden mb-6">
                    <label for="examDuration" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-clock mr-1"></i>
                        مدة الامتحان (بالدقائق)
                    </label>
                    <input type="number" id="examDuration" min="5" max="180" value="60" class="input-field">
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-center space-x-4 space-x-reverse">
                <button id="generateExamBtn" class="btn-primary text-lg px-8 py-3 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <i class="fas fa-magic mr-2"></i>
                    إنشاء الامتحان
                </button>
                <button id="resetBtn" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                    <i class="fas fa-redo mr-2"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>
    </main>

    <!-- Progress Modal -->
    <div id="progressModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="loading-spinner mx-auto mb-4"></div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">جاري تحليل الملف...</h3>
                <p id="progressText" class="text-gray-600 mb-4">يرجى الانتظار بينما نقوم بتحليل محتوى PDF</p>
                
                <!-- Progress Bar -->
                <div class="progress-bar mb-4">
                    <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
                
                <div id="progressSteps" class="text-sm text-gray-500 space-y-1">
                    <div id="step1" class="flex items-center">
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        <span>رفع الملف...</span>
                    </div>
                    <div id="step2" class="flex items-center text-gray-300">
                        <i class="fas fa-circle mr-2"></i>
                        <span>تحليل المحتوى...</span>
                    </div>
                    <div id="step3" class="flex items-center text-gray-300">
                        <i class="fas fa-circle mr-2"></i>
                        <span>توليد الأسئلة...</span>
                    </div>
                    <div id="step4" class="flex items-center text-gray-300">
                        <i class="fas fa-circle mr-2"></i>
                        <span>إنهاء المعالجة...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="js/supabase-config.js"></script>
    <script type="module" src="js/upload.js"></script>
</body>
</html>
