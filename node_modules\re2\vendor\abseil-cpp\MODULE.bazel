# Copyright 2024 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# https://bazel.build/external/overview#bzlmod

module(
    name = "abseil-cpp",
    version = "20250127.1",
    compatibility_level = 1,
)

cc_configure = use_extension("@rules_cc//cc:extensions.bzl",
                             "cc_configure_extension",
                             dev_dependency = True)
use_repo(cc_configure, "local_config_cc")

# Only direct dependencies need to be listed below.
# Please keep the versions in sync with the versions in the WORKSPACE file.

bazel_dep(name = "rules_cc", version = "0.0.17")
bazel_dep(name = "bazel_skylib", version = "1.7.1")
bazel_dep(name = "platforms", version = "0.0.10")

bazel_dep(
    name = "google_benchmark",
    version = "1.8.5",
    dev_dependency = True,
)

# Note: Googletest is NOT a dev_dependency. Some Abseil test utilities
# intended to be used by Abseil users depend on GoogleTest.
bazel_dep(
    name = "googletest",
    version = "1.15.2",
)
