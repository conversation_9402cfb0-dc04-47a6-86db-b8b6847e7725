# دليل إعداد Supabase

## 🚀 الخطوات المطلوبة لإعداد Supabase

### 1. إنشاء مشروع Supabase جديد

1. اذهب إلى: https://supabase.com/dashboard
2. انقر على "New Project"
3. اختر Organization أو أنشئ واحدة جديدة
4. أدخل تفاصيل المشروع:
   - **Name**: PDF to Exam Converter
   - **Database Password**: كلمة مرور قوية (احفظها!)
   - **Region**: اختر أقرب منطقة (مثل Singapore أو Frankfurt)
5. انقر على "Create new project"
6. انتظر حتى يكتمل إعداد المشروع (2-3 دقائق)

### 2. إعداد قاعدة البيانات

1. في لوحة تحكم Supabase، اذهب إلى **SQL Editor**
2. انسخ محتوى ملف `supabase-schema.sql` والصقه في المحرر
3. انقر على "Run" لتنفيذ الـ SQL
4. تأكد من عدم وجود أخطاء في التنفيذ

### 3. إعداد Storage

1. اذهب إلى **Storage** في الشريط الجانبي
2. انقر على "Create a new bucket"
3. أنشئ bucket باسم `pdf-files`:
   - **Name**: pdf-files
   - **Public bucket**: اتركه غير مفعل (Private)
   - انقر على "Create bucket"
4. أنشئ bucket آخر باسم `exam-exports`:
   - **Name**: exam-exports
   - **Public bucket**: اتركه غير مفعل (Private)
   - انقر على "Create bucket"

### 4. إعداد Storage Policies

1. في صفحة Storage، انقر على bucket `pdf-files`
2. اذهب إلى تبويب **Policies**
3. انقر على "New Policy" وأضف السياسات التالية:

#### سياسة رفع الملفات:
```sql
CREATE POLICY "Users can upload their own PDF files" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'pdf-files' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);
```

#### سياسة عرض الملفات:
```sql
CREATE POLICY "Users can view their own PDF files" ON storage.objects
FOR SELECT USING (
  bucket_id = 'pdf-files' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);
```

#### سياسة حذف الملفات:
```sql
CREATE POLICY "Users can delete their own PDF files" ON storage.objects
FOR DELETE USING (
  bucket_id = 'pdf-files' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);
```

4. كرر نفس العملية لـ bucket `exam-exports` (استبدل `pdf-files` بـ `exam-exports`)

### 5. إعداد Authentication

1. اذهب إلى **Authentication** > **Settings**
2. في تبويب **General**:
   - تأكد من تفعيل "Enable email confirmations"
   - اضبط "Site URL" إلى: `https://ahmed-962e0.web.app`
   - أضف "Redirect URLs": `https://ahmed-962e0.web.app/dashboard.html`

3. في تبويب **Providers**:
   - تأكد من تفعيل "Email"
   - لتفعيل Google OAuth:
     - انقر على "Google"
     - فعّل "Enable Google provider"
     - ستحتاج إلى Google OAuth credentials من Google Cloud Console

### 6. إعداد Google OAuth (اختياري)

1. اذهب إلى: https://console.cloud.google.com
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Google+ API
4. اذهب إلى **Credentials** > **Create Credentials** > **OAuth 2.0 Client IDs**
5. اختر "Web application"
6. أضف Authorized redirect URIs:
   - `https://your-project-ref.supabase.co/auth/v1/callback`
7. انسخ Client ID و Client Secret
8. ارجع إلى Supabase > Authentication > Providers > Google
9. الصق Client ID و Client Secret
10. احفظ التغييرات

### 7. الحصول على مفاتيح المشروع

1. اذهب إلى **Settings** > **API**
2. انسخ القيم التالية:
   - **Project URL**: `https://your-project-ref.supabase.co`
   - **anon public key**: مفتاح طويل يبدأ بـ `eyJ...`

### 8. تحديث تكوين التطبيق

1. افتح ملف `public/js/supabase-config.js`
2. استبدل القيم التالية:

```javascript
const supabaseUrl = 'https://your-project-ref.supabase.co';
const supabaseAnonKey = 'your-anon-key-here';
```

بالقيم الفعلية من مشروعك.

### 9. اختبار الإعداد

1. افتح الموقع: https://ahmed-962e0.web.app
2. جرب إنشاء حساب جديد
3. تحقق من وصول رسالة تأكيد البريد الإلكتروني
4. جرب تسجيل الدخول
5. تحقق من وصولك إلى لوحة التحكم

## 🔧 استكشاف الأخطاء

### مشكلة: "Invalid API key"
- تأكد من صحة supabaseUrl و supabaseAnonKey
- تحقق من عدم وجود مسافات إضافية

### مشكلة: "Row Level Security policy violation"
- تأكد من تنفيذ جميع الـ SQL policies
- تحقق من تسجيل الدخول قبل محاولة الوصول للبيانات

### مشكلة: "Storage bucket not found"
- تأكد من إنشاء buckets بالأسماء الصحيحة
- تحقق من Storage policies

### مشكلة: "Google OAuth not working"
- تأكد من صحة Google OAuth credentials
- تحقق من Authorized redirect URIs

## 📊 مراقبة الاستخدام

- **Database**: اذهب إلى Settings > Usage لمراقبة استخدام قاعدة البيانات
- **Storage**: مراقبة حجم الملفات المرفوعة
- **Auth**: عدد المستخدمين المسجلين
- **API Requests**: عدد طلبات API

## 🔒 الأمان

- جميع البيانات محمية بـ Row Level Security (RLS)
- كل مستخدم يمكنه الوصول فقط لبياناته الخاصة
- الملفات محفوظة في Storage خاص
- مفاتيح API آمنة ومحدودة الصلاحيات

## 💰 التكلفة

- **Free Tier**: يشمل:
  - 500MB قاعدة بيانات
  - 1GB تخزين ملفات
  - 50,000 مستخدم نشط شهرياً
  - 2 مليون طلب API شهرياً

- **Pro Plan**: $25/شهر للاستخدام المتقدم

## 🎯 الخطوات التالية

بعد إكمال الإعداد:

1. ✅ اختبر جميع الوظائف
2. ✅ تأكد من عمل المصادقة
3. ✅ جرب رفع ملف PDF
4. ⏳ أكمل تطوير باقي الصفحات
5. ⏳ أضف المزيد من الميزات

---

**تهانينا! 🎉** 
مشروعك الآن يعمل مع Supabase بنجاح!
