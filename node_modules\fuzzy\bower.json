{"name": "fuzzy", "version": "0.1.1", "homepage": "https://github.com/mattyork/fuzzy", "authors": ["<PERSON> <<EMAIL>>"], "description": "small, standalone fuzzy search / fuzzy filter like sublime text Cmd-P. browser or node.", "main": "lib/fuzzy.js", "keywords": ["fuzzy", "search", "filter", "sublime", "sublime text"], "license": "MIT", "devDependencies": {"mocha": ">= 1.3.0", "chai": ">= 1.1.1", "underscore": ">= 1.3.3", "uglify-js": ">= 1.3.2", "jshint": ">= 0.7.1"}, "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", "examples"]}