<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - محول PDF إلى امتحانات</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50 font-arabic">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center">
                        <i class="fas fa-file-pdf text-primary-600 text-2xl mr-2"></i>
                        <span class="text-xl font-bold text-gray-900">محول PDF</span>
                    </a>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button id="newExamBtn" class="btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        امتحان جديد
                    </button>
                    
                    <div class="relative">
                        <button id="userMenuBtn" class="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-gray-900 focus:outline-none">
                            <img id="userAvatar" src="" alt="User Avatar" class="w-8 h-8 rounded-full">
                            <span id="userName" class="hidden sm:block"></span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        
                        <div id="userDropdown" class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user mr-2"></i>
                                الملف الشخصي
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-cog mr-2"></i>
                                الإعدادات
                            </a>
                            <hr class="my-1">
                            <button id="logoutBtn" class="block w-full text-right px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>
                                تسجيل الخروج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Section -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">مرحباً بك في لوحة التحكم</h1>
            <p class="text-gray-600">إدارة امتحاناتك ومتابعة النتائج</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-file-alt text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">إجمالي الامتحانات</p>
                        <p id="totalExams" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">المحاولات المكتملة</p>
                        <p id="totalAttempts" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-star text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">متوسط النتائج</p>
                        <p id="averageScore" class="text-2xl font-bold text-gray-900">0%</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">آخر نشاط</p>
                        <p id="lastActivity" class="text-sm font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">إجراءات سريعة</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button id="uploadPDFBtn" class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-colors duration-200">
                        <div class="text-center">
                            <i class="fas fa-upload text-2xl text-gray-400 mb-2"></i>
                            <p class="text-sm font-medium text-gray-600">رفع ملف PDF جديد</p>
                        </div>
                    </button>
                    
                    <button id="browseTemplatesBtn" class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-colors duration-200">
                        <div class="text-center">
                            <i class="fas fa-templates text-2xl text-gray-400 mb-2"></i>
                            <p class="text-sm font-medium text-gray-600">تصفح القوالب</p>
                        </div>
                    </button>
                    
                    <button id="viewAnalyticsBtn" class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-colors duration-200">
                        <div class="text-center">
                            <i class="fas fa-chart-bar text-2xl text-gray-400 mb-2"></i>
                            <p class="text-sm font-medium text-gray-600">عرض التحليلات</p>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Recent Exams -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-900">الامتحانات الأخيرة</h2>
                <button id="viewAllExamsBtn" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                    عرض الكل
                    <i class="fas fa-arrow-left mr-1"></i>
                </button>
            </div>
            <div class="p-6">
                <div id="recentExamsList" class="space-y-4">
                    <!-- Recent exams will be loaded here -->
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-file-alt text-4xl mb-4"></i>
                        <p>لا توجد امتحانات بعد</p>
                        <button class="btn-primary mt-4" onclick="window.location.href='upload.html'">
                            إنشاء أول امتحان
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Results -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-900">النتائج الأخيرة</h2>
                <button id="viewAllResultsBtn" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                    عرض الكل
                    <i class="fas fa-arrow-left mr-1"></i>
                </button>
            </div>
            <div class="p-6">
                <div id="recentResultsList" class="space-y-4">
                    <!-- Recent results will be loaded here -->
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-chart-line text-4xl mb-4"></i>
                        <p>لا توجد نتائج بعد</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 ml-3"></div>
            <span class="text-gray-700">جاري التحميل...</span>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="js/supabase-config.js"></script>
    <script type="module" src="js/dashboard.js"></script>
</body>
</html>
