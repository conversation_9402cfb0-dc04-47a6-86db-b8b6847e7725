{"version": 3, "file": "googleError.js", "sourceRoot": "", "sources": ["../../src/googleError.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,qCAA2D;AAC3D,uCAAuC;AAEvC,qDAAqD;AACrD,yCAAkD;AAGlD,MAAa,WAAY,SAAQ,KAAK;IASpC,sEAAsE;IACtE,yCAAyC;IACzC,MAAM,CAAC,sBAAsB,CAAC,GAAgB;QAC5C,MAAM,OAAO,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACzC,IAAI,CAAC;YACH,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,yBAAyB,CAAC,EAAE,CAAC;gBAChE,MAAM,gBAAgB,GACpB,OAAO,CAAC,uBAAuB,CAC7B,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,yBAAyB,CAAO,CAClD,CAAC;gBACJ,IACE,gBAAgB;oBAChB,gBAAgB,CAAC,OAAO;oBACxB,gBAAgB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EACnC,CAAC;oBACD,GAAG,CAAC,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC;gBAC/C,CAAC;gBACD,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,SAAS,EAAE,CAAC;oBACnD,GAAG,CAAC,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC/C,GAAG,CAAC,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC/C,GAAG,CAAC,iBAAiB,GAAG,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC;gBAC9D,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,SAAS,EAAE,CAAC;YACnB,qBAAqB;QACvB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,mEAAmE;IACnE,8DAA8D;IAC9D,MAAM,CAAC,cAAc,CAAC,IAAS;QAC7B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,8DAA8D;YAC9D,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAQ,EAAE,EAAE;gBAC5B,OAAO,OAAO,IAAI,GAAG,CAAC;YACxB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,sEAAsE;QACtE,oEAAoE;QACpE,sFAAsF;QACtF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YAEnB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,OAAO,CAAC;iBAC9B,OAAO,CAAC,GAAG,CAAC,EAAE;gBACb,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;QACP,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3D,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,EACzC,WAAW,CACZ,CAAC;QACF,2CAA2C;QAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,KAAK,CAAC,IAAI,GAAG,IAAA,kCAAyB,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAChE,CAAC;aAAM,CAAC;YACN,wEAAwE;YACxE,gCAAgC;YAChC,OAAO,KAAK,CAAC,IAAI,CAAC;QACpB,CAAC;QACD,0FAA0F;QAC1F,uCAAuC;QACvC,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC;gBACH,MAAM,gBAAgB,GACpB,OAAO,CAAC,uBAAuB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACjD,IACE,gBAAgB;oBAChB,gBAAgB,CAAC,OAAO;oBACxB,gBAAgB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EACnC,CAAC;oBACD,KAAK,CAAC,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC;gBACjD,CAAC;gBACD,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,SAAS,EAAE,CAAC;oBACnD,KAAK,CAAC,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC;oBACjD,KAAK,CAAC,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC;oBACjD,gEAAgE;oBAChE,iEAAiE;oBACjE,uDAAuD;oBACvD,KAAK,CAAC,iBAAiB,GAAG,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC;gBAChE,CAAC;YACH,CAAC;YAAC,OAAO,SAAS,EAAE,CAAC;gBACnB,qBAAqB;YACvB,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAxGD,kCAwGC;AAkCD,MAAa,kBAAkB;IAK7B;QACE,8DAA8D;QAC9D,MAAM,cAAc,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACjE,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;QAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;IAC9D,CAAC;IAED,iBAAiB,CAAC,QAAqB;QACrC,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QACpE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CACb,gDAAgD,QAAQ,CAAC,QAAQ,EAAE,CACpE,CAAC;QACJ,CAAC;QACD,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,yEAAyE;IACzE,eAAe,CAAC,MAA4B;QAC1C,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAyB,CAAC;QAE1E,6DAA6D;QAC7D,iCAAiC;QACjC,MAAM,OAAO,GAA4B,EAAE,CAAC;QAC5C,IAAI,SAAS,CAAC;QACd,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBACrD,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC5B,IAAI,MAAM,CAAC,QAAQ,KAAK,0CAA0C,EAAE,CAAC;oBACnE,SAAS,GAAG,aAAqC,CAAC;gBACpD,CAAC;YACH,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,0EAA0E;YAC5E,CAAC;QACH,CAAC;QACD,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,aAAa,EAAE,OAAO;YACtB,MAAM,EAAE,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,MAAM;YACzB,MAAM,EAAE,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,MAAM;YACzB,iBAAiB,EAAE,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,QAAQ;SACvC,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,0CAA0C;IAC1C,4FAA4F;IAC5F,mBAAmB,CAAC,MAA4B;QAC9C,MAAM,CAAC,OAAO,GAAG,GAAG,MAAM,CAAC,IAAI,IAAI,eAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;QAC5E,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC;IAChE,CAAC;IAED,yEAAyE;IACzE,mEAAmE;IACnE,qBAAqB,CAAC,MAA4B;QAChD,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,iFAAiF;IACjF,uBAAuB,CACrB,SAAmC;QAEnC,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,IAAI,SAAS,CAAC;QACd,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CACtC,UAAU,CACa,CAAC;YAC1B,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;gBACvC,IAAI,CAAC;oBACH,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;oBACrD,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC5B,IAAI,MAAM,CAAC,QAAQ,KAAK,0CAA0C,EAAE,CAAC;wBACnE,SAAS,GAAG,aAAqC,CAAC;oBACpD,CAAC;gBACH,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,0EAA0E;gBAC5E,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,MAAM,GAAG;YACb,OAAO;YACP,SAAS;SACV,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,gEAAgE;IAChE,eAAe,CAAC,IAAe;QAC7B,MAAM,YAAY,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CACb,0BAA0B,IAAI,6CAA6C,CAC5E,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,EAAE,iCAAsB,CAAC,CAAC;IACxE,CAAC;IAED,iFAAiF;IACjF,uBAAuB,CACrB,UAA8B;QAE9B,6DAA6D;QAC7D,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,IAAI,SAAS,CAAC;QACd,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;YAChC,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBACrD,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC5B,IAAI,MAAM,CAAC,QAAQ,KAAK,0CAA0C,EAAE,CAAC;oBACnE,SAAS,GAAG,aAAqC,CAAC;gBACpD,CAAC;YACH,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,0EAA0E;YAC5E,CAAC;QACH,CAAC;QACD,OAAO,EAAC,OAAO,EAAE,SAAS,EAAC,CAAC;IAC9B,CAAC;CACF;AArID,gDAqIC"}