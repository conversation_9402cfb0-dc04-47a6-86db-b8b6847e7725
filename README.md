# محول PDF إلى امتحانات تفاعلية

تطبيق ويب متقدم لتحويل ملفات PDF إلى امتحانات تفاعلية باستخدام الذكاء الاصطناعي (Gemini AI) و Supabase.

## 🌟 المميزات

- **تحليل PDF ذكي**: استخراج المحتوى من ملفات PDF وتحليله باستخدام Gemini AI
- **توليد أسئلة متنوعة**: اختيار من متعدد، صح وخطأ، املأ الفراغ
- **واجهة عربية**: دعم كامل للغة العربية مع تصميم RTL
- **تخصيص الامتحانات**: إعدادات مرونة لعدد الأسئلة ومستوى الصعوبة
- **معاينة وتعديل**: إمكانية مراجعة وتعديل الأسئلة قبل النشر
- **حفظ سحابي**: حفظ الامتحانات في Supabase Database
- **مصادقة آمنة**: نظام تسجيل دخول متقدم مع Google OAuth
- **لوحة تحكم**: إدارة شاملة للامتحانات والنتائج

## 🚀 الموقع المباشر

الموقع متاح الآن على: **https://ahmed-962e0.web.app** (Firebase Hosting)

## 🛠️ التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+), Tailwind CSS
- **Backend**: Supabase (Database, Storage, Auth, Real-time)
- **Hosting**: Firebase Hosting (يمكن نقله إلى Vercel أو Netlify)
- **AI**: Google Gemini API للتحليل وتوليد الأسئلة
- **Authentication**: Supabase Auth مع Google Sign-in

## 📁 هيكل المشروع

```
pdf-to-exam-converter/
├── public/                 # ملفات الواجهة الأمامية
│   ├── index.html         # الصفحة الرئيسية
│   ├── auth.html          # صفحة المصادقة
│   ├── upload.html        # صفحة رفع الملفات
│   ├── exam-preview.html  # صفحة معاينة الامتحان
│   ├── css/
│   │   └── style.css      # ملف التنسيقات
│   └── js/
│       ├── firebase-config.js  # تكوين Firebase
│       ├── main.js            # الوظائف العامة
│       ├── upload.js          # وظائف رفع الملفات
│       └── exam-preview.js    # وظائف معاينة الامتحان
├── functions/             # Cloud Functions
│   ├── index.js          # وظائف تحليل PDF وتوليد الأسئلة
│   └── package.json      # تبعيات Cloud Functions
├── firestore.rules       # قواعد أمان Firestore
├── storage.rules         # قواعد أمان Storage
├── firebase.json         # تكوين Firebase
└── package.json          # تبعيات المشروع
```

## ⚙️ إعداد المشروع

### 1. متطلبات النظام
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn
- حساب Firebase
- مفتاح Gemini API

### 2. تثبيت التبعيات
```bash
npm install
cd functions && npm install
```

### 3. إعداد Firebase

#### أ. إنشاء مشروع Firebase جديد
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. أنشئ مشروع جديد
3. فعّل الخدمات التالية:
   - **Authentication**: فعّل Google Sign-in
   - **Firestore Database**: أنشئ قاعدة بيانات في وضع الاختبار
   - **Storage**: فعّل Firebase Storage
   - **Functions**: ترقية إلى خطة Blaze (مطلوب للـ Cloud Functions)

#### ب. تحديث تكوين Firebase
1. انسخ تكوين Firebase من Project Settings
2. حدث الملف `public/js/firebase-config.js` بالقيم الصحيحة:

```javascript
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};
```

### 4. إعداد Gemini API
1. احصل على مفتاح API من [Google AI Studio](https://makersuite.google.com/app/apikey)
2. حدث المفتاح في `public/js/firebase-config.js`:

```javascript
const GEMINI_API_KEY = 'your-gemini-api-key';
```

### 5. رفع المشروع

#### رفع الموقع فقط (Hosting)
```bash
firebase deploy --only hosting
```

#### رفع جميع الخدمات
```bash
firebase deploy
```

## 🔧 التطوير المحلي

### تشغيل الخادم المحلي
```bash
npm start
# أو
firebase serve
```

### تشغيل المحاكيات (Emulators)
```bash
npm run emulators
# أو
firebase emulators:start
```

### بناء ملفات CSS
```bash
npm run build:css
```

## 📋 الوظائف الحالية

### ✅ مكتملة
- [x] الصفحة الرئيسية مع تصميم احترافي
- [x] نظام المصادقة (تسجيل دخول/إنشاء حساب)
- [x] رفع ملفات PDF مع التحقق من الصحة
- [x] تحليل PDF باستخدام Cloud Functions
- [x] تكامل Gemini AI لتوليد الأسئلة
- [x] صفحة معاينة الامتحان مع إمكانية التعديل
- [x] حفظ الامتحانات في Firestore
- [x] رفع المشروع على Firebase Hosting

### 🔄 قيد التطوير
- [ ] صفحة حل الامتحان التفاعلية
- [ ] صفحة عرض النتائج والتقييم
- [ ] لوحة تحكم المستخدم
- [ ] تحسينات الأداء والتصميم

## 🔐 الأمان

- قواعد Firestore تضمن أن كل مستخدم يمكنه الوصول فقط لبياناته
- قواعد Storage تسمح برفع ملفات PDF فقط للمستخدمين المصادق عليهم
- التحقق من صحة الملفات في الواجهة الأمامية والخلفية
- تشفير البيانات أثناء النقل والتخزين

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في تحميل Firebase**
   - تأكد من صحة تكوين Firebase في `firebase-config.js`
   - تحقق من تفعيل الخدمات المطلوبة في Firebase Console

2. **فشل رفع الملفات**
   - تأكد من تفعيل Firebase Storage
   - تحقق من قواعد Storage

3. **خطأ في Cloud Functions**
   - تأكد من ترقية المشروع إلى خطة Blaze
   - تحقق من صحة مفتاح Gemini API

4. **مشاكل في المصادقة**
   - تأكد من تفعيل Google Sign-in في Firebase Auth
   - تحقق من إعدادات OAuth

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى:
1. التحقق من قسم استكشاف الأخطاء أعلاه
2. مراجعة Firebase Console للتأكد من تفعيل جميع الخدمات
3. التأكد من صحة مفاتيح API

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**ملاحظة**: هذا المشروع في مرحلة التطوير النشط. بعض الميزات قد تكون غير مكتملة أو تحتاج إلى تحسينات إضافية.
