<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - محول PDF إلى امتحانات</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase -->
    <script type="module" src="js/firebase-config.js"></script>
</head>
<body class="font-arabic bg-gray-50 min-h-screen flex items-center justify-center">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-50 to-secondary-50 opacity-50"></div>
    
    <!-- Auth Container -->
    <div class="relative z-10 w-full max-w-md mx-auto p-6">
        <!-- Logo -->
        <div class="text-center mb-8">
            <a href="index.html" class="inline-block">
                <h1 class="text-3xl font-bold text-primary-600">
                    <i class="fas fa-file-pdf mr-2"></i>
                    محول PDF
                </h1>
            </a>
        </div>
        
        <!-- Auth Card -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <!-- Tab Navigation -->
            <div class="flex mb-6 bg-gray-100 rounded-lg p-1">
                <button id="loginTab" class="flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 font-medium">
                    تسجيل الدخول
                </button>
                <button id="signupTab" class="flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 font-medium">
                    إنشاء حساب
                </button>
            </div>
            
            <!-- Login Form -->
            <div id="loginForm" class="auth-form">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">مرحباً بعودتك</h2>
                
                <form id="loginFormElement">
                    <div class="mb-4">
                        <label for="loginEmail" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني
                        </label>
                        <input type="email" id="loginEmail" name="email" required 
                               class="input-field" placeholder="أدخل بريدك الإلكتروني">
                    </div>
                    
                    <div class="mb-6">
                        <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور
                        </label>
                        <div class="relative">
                            <input type="password" id="loginPassword" name="password" required 
                                   class="input-field pl-10" placeholder="أدخل كلمة المرور">
                            <button type="button" id="toggleLoginPassword" 
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between mb-6">
                        <label class="flex items-center">
                            <input type="checkbox" id="rememberMe" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="mr-2 text-sm text-gray-600">تذكرني</span>
                        </label>
                        <a href="#" id="forgotPassword" class="text-sm text-primary-600 hover:text-primary-500">
                            نسيت كلمة المرور؟
                        </a>
                    </div>
                    
                    <button type="submit" id="loginBtn" class="btn-primary w-full mb-4">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        تسجيل الدخول
                    </button>
                </form>
                
                <!-- Divider -->
                <div class="relative my-6">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">أو</span>
                    </div>
                </div>
                
                <!-- Google Sign In -->
                <button id="googleSignInBtn" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="w-5 h-5 ml-2">
                    تسجيل الدخول بـ Google
                </button>
            </div>
            
            <!-- Signup Form -->
            <div id="signupForm" class="auth-form hidden">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">إنشاء حساب جديد</h2>
                
                <form id="signupFormElement">
                    <div class="mb-4">
                        <label for="signupName" class="block text-sm font-medium text-gray-700 mb-2">
                            الاسم الكامل
                        </label>
                        <input type="text" id="signupName" name="name" required 
                               class="input-field" placeholder="أدخل اسمك الكامل">
                    </div>
                    
                    <div class="mb-4">
                        <label for="signupEmail" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني
                        </label>
                        <input type="email" id="signupEmail" name="email" required 
                               class="input-field" placeholder="أدخل بريدك الإلكتروني">
                    </div>
                    
                    <div class="mb-4">
                        <label for="signupPassword" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور
                        </label>
                        <div class="relative">
                            <input type="password" id="signupPassword" name="password" required 
                                   class="input-field pl-10" placeholder="أدخل كلمة المرور">
                            <button type="button" id="toggleSignupPassword" 
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">يجب أن تحتوي على 8 أحرف على الأقل</p>
                    </div>
                    
                    <div class="mb-6">
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                            تأكيد كلمة المرور
                        </label>
                        <div class="relative">
                            <input type="password" id="confirmPassword" name="confirmPassword" required 
                                   class="input-field pl-10" placeholder="أعد إدخال كلمة المرور">
                            <button type="button" id="toggleConfirmPassword" 
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label class="flex items-start">
                            <input type="checkbox" id="agreeTerms" required class="rounded border-gray-300 text-primary-600 focus:ring-primary-500 mt-1">
                            <span class="mr-2 text-sm text-gray-600">
                                أوافق على <a href="#" class="text-primary-600 hover:text-primary-500">شروط الاستخدام</a> 
                                و <a href="#" class="text-primary-600 hover:text-primary-500">سياسة الخصوصية</a>
                            </span>
                        </label>
                    </div>
                    
                    <button type="submit" id="signupBtn" class="btn-primary w-full mb-4">
                        <i class="fas fa-user-plus mr-2"></i>
                        إنشاء حساب
                    </button>
                </form>
                
                <!-- Divider -->
                <div class="relative my-6">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">أو</span>
                    </div>
                </div>
                
                <!-- Google Sign Up -->
                <button id="googleSignUpBtn" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="w-5 h-5 ml-2">
                    التسجيل بـ Google
                </button>
            </div>
        </div>
        
        <!-- Back to Home -->
        <div class="text-center mt-6">
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors duration-200">
                <i class="fas fa-arrow-right mr-1"></i>
                العودة إلى الصفحة الرئيسية
            </a>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="loading-spinner mx-auto mb-4"></div>
            <p class="text-gray-600">جاري المعالجة...</p>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
</body>
</html>
