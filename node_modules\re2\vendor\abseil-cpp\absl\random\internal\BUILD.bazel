#
# Copyright 2019 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load("@bazel_skylib//lib:selects.bzl", "selects")

# Internal-only implementation classes for Abseil Random
load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_DEFAULT_LINKOPTS",
    "ABSL_TEST_COPTS",
)

default_package_visibility = [
    "//absl/random:__pkg__",
]

package(
    default_visibility = default_package_visibility,
    features = [
        "header_modules",
        "layering_check",
        "parse_headers",
    ],
)

licenses(["notice"])

# Used to select on compilers that support GCC-compatible options
# (e.g. "-maes").
selects.config_setting_group(
    name = "gcc_compatible",
    match_any = [
        "@rules_cc//cc/compiler:clang",
        "@rules_cc//cc/compiler:gcc",
    ],
)

selects.config_setting_group(
    name = "gcc_compatible-aarch32",
    match_all = [
        ":gcc_compatible",
        "@platforms//cpu:aarch32",
    ],
)

selects.config_setting_group(
    name = "gcc_compatible-aarch64",
    match_all = [
        ":gcc_compatible",
        "@platforms//cpu:aarch64",
    ],
)

selects.config_setting_group(
    name = "ppc_crypto",
    match_any = [
        "@platforms//cpu:ppc",
        "@platforms//cpu:ppc32",
        "@platforms//cpu:ppc64le",
    ],
)

selects.config_setting_group(
    name = "gcc_compatible-ppc_crypto",
    match_all = [
        ":gcc_compatible",
        ":ppc_crypto",
    ],
)

selects.config_setting_group(
    name = "gcc_compatible-x86_64",
    match_all = [
        ":gcc_compatible",
        "@platforms//cpu:x86_64",
    ],
)

# Some libraries are compiled with options to generate AES-NI
# instructions, and runtime dispatch is used to determine if the host
# microarchitecture supports AES-NI or if a portable fallback library
# should be called.
ABSL_RANDOM_RANDEN_COPTS = select({
    ":gcc_compatible-aarch32": ["-mfpu=neon"],
    ":gcc_compatible-aarch64": ["-march=armv8-a+crypto"],
    ":gcc_compatible-ppc_crypto": ["-mcrypto"],
    ":gcc_compatible-x86_64": [
        "-maes",
        "-msse4.1",
    ],
    "//conditions:default": [],
})

cc_library(
    name = "traits",
    hdrs = ["traits.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/numeric:bits",
        "//absl/numeric:int128",
    ],
)

cc_library(
    name = "distribution_caller",
    hdrs = ["distribution_caller.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:fast_type_id",
        "//absl/meta:type_traits",
        "//absl/utility",
    ],
)

cc_library(
    name = "fast_uniform_bits",
    hdrs = [
        "fast_uniform_bits.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":traits",
        "//absl/base:config",
        "//absl/meta:type_traits",
    ],
)

cc_library(
    name = "seed_material",
    srcs = [
        "seed_material.cc",
    ],
    hdrs = [
        "seed_material.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS + select({
        "@rules_cc//cc/compiler:msvc-cl": ["-DEFAULTLIB:bcrypt.lib"],
        "@rules_cc//cc/compiler:clang-cl": ["-DEFAULTLIB:bcrypt.lib"],
        "//absl:mingw_compiler": [
            "-DEFAULTLIB:bcrypt.lib",
            "-lbcrypt",
        ],
        "//conditions:default": [],
    }),
    deps = [
        ":fast_uniform_bits",
        "//absl/base:core_headers",
        "//absl/base:dynamic_annotations",
        "//absl/base:raw_logging_internal",
        "//absl/strings",
        "//absl/types:optional",
        "//absl/types:span",
    ],
)

cc_library(
    name = "pool_urbg",
    srcs = [
        "pool_urbg.cc",
    ],
    hdrs = [
        "pool_urbg.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = select({
        "@rules_cc//cc/compiler:msvc-cl": [],
        "@rules_cc//cc/compiler:clang-cl": [],
        "@rules_cc//cc/compiler:emscripten": [],
        "//conditions:default": ["-pthread"],
    }) + ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":randen",
        ":seed_material",
        ":traits",
        "//absl/base",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:endian",
        "//absl/base:raw_logging_internal",
        "//absl/random:seed_gen_exception",
        "//absl/types:span",
    ],
)

cc_library(
    name = "explicit_seed_seq",
    testonly = True,
    hdrs = [
        "explicit_seed_seq.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:endian",
    ],
)

cc_library(
    name = "sequence_urbg",
    testonly = True,
    hdrs = [
        "sequence_urbg.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = ["//absl/base:config"],
)

cc_library(
    name = "salted_seed_seq",
    hdrs = [
        "salted_seed_seq.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":seed_material",
        "//absl/container:inlined_vector",
        "//absl/meta:type_traits",
        "//absl/types:optional",
        "//absl/types:span",
    ],
)

cc_library(
    name = "iostream_state_saver",
    hdrs = ["iostream_state_saver.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/meta:type_traits",
        "//absl/numeric:int128",
    ],
)

cc_library(
    name = "generate_real",
    hdrs = [
        "generate_real.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":fastmath",
        ":traits",
        "//absl/meta:type_traits",
        "//absl/numeric:bits",
    ],
)

cc_library(
    name = "fastmath",
    hdrs = [
        "fastmath.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = ["//absl/numeric:bits"],
)

cc_library(
    name = "wide_multiply",
    hdrs = ["wide_multiply.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":traits",
        "//absl/base:config",
        "//absl/numeric:bits",
        "//absl/numeric:int128",
    ],
)

cc_library(
    name = "nonsecure_base",
    hdrs = ["nonsecure_base.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":pool_urbg",
        ":salted_seed_seq",
        ":seed_material",
        "//absl/base:core_headers",
        "//absl/container:inlined_vector",
        "//absl/meta:type_traits",
        "//absl/types:span",
    ],
)

cc_library(
    name = "pcg_engine",
    hdrs = ["pcg_engine.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":fastmath",
        ":iostream_state_saver",
        "//absl/base:config",
        "//absl/meta:type_traits",
        "//absl/numeric:bits",
        "//absl/numeric:int128",
    ],
)

cc_library(
    name = "randen_engine",
    hdrs = ["randen_engine.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = default_package_visibility + [
    ],
    deps = [
        ":iostream_state_saver",
        ":randen",
        "//absl/base:endian",
        "//absl/meta:type_traits",
    ],
)

cc_library(
    name = "platform",
    srcs = [
        "randen_round_keys.cc",
    ],
    hdrs = [
        "randen_traits.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    textual_hdrs = [
        "platform.h",
    ],
    deps = ["//absl/base:config"],
)

cc_library(
    name = "randen",
    srcs = [
        "randen.cc",
    ],
    hdrs = [
        "randen.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":platform",
        ":randen_hwaes",
        ":randen_slow",
        "//absl/base:raw_logging_internal",
    ],
)

cc_library(
    name = "randen_slow",
    srcs = ["randen_slow.cc"],
    hdrs = ["randen_slow.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":platform",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:endian",
        "//absl/numeric:int128",
    ],
)

cc_library(
    name = "randen_hwaes",
    srcs = [
        "randen_detect.cc",
    ],
    hdrs = [
        "randen_detect.h",
        "randen_hwaes.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":platform",
        ":randen_hwaes_impl",
        "//absl/base:config",
        "//absl/types:optional",
    ],
)

# build with --save_temps to see assembly language output.
cc_library(
    name = "randen_hwaes_impl",
    srcs = [
        "randen_hwaes.cc",
        "randen_hwaes.h",
    ],
    copts = ABSL_DEFAULT_COPTS + ABSL_RANDOM_RANDEN_COPTS + select({
        "@rules_cc//cc/compiler:msvc-cl": [],
        "@rules_cc//cc/compiler:clang-cl": [],
        "//conditions:default": ["-Wno-pass-failed"],
    }),
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":platform",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/numeric:int128",
    ],
)

cc_binary(
    name = "gaussian_distribution_gentables",
    srcs = [
        "gaussian_distribution_gentables.cc",
    ],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:core_headers",
        "//absl/random:distributions",
    ],
)

cc_library(
    name = "distribution_test_util",
    testonly = True,
    srcs = [
        "chi_square.cc",
        "distribution_test_util.cc",
    ],
    hdrs = [
        "chi_square.h",
        "distribution_test_util.h",
    ],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
        "//absl/strings",
        "//absl/strings:str_format",
        "//absl/types:span",
    ],
)

# Common tags for tests, etc.
ABSL_RANDOM_NONPORTABLE_TAGS = [
    "no_test_android_arm",
    "no_test_android_arm64",
    "no_test_android_x86",
    "no_test_darwin_x86_64",
    "no_test_ios_x86_64",
    "no_test_loonix",
    "no_test_lexan",
    "no_test_wasm",
]

cc_test(
    name = "traits_test",
    size = "small",
    srcs = ["traits_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":traits",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "generate_real_test",
    size = "small",
    srcs = [
        "generate_real_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":generate_real",
        "//absl/flags:flag",
        "//absl/numeric:bits",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "distribution_test_util_test",
    size = "small",
    srcs = ["distribution_test_util_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":distribution_test_util",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "fastmath_test",
    size = "small",
    srcs = ["fastmath_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":fastmath",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "explicit_seed_seq_test",
    size = "small",
    srcs = ["explicit_seed_seq_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":explicit_seed_seq",
        "//absl/random:seed_sequences",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "salted_seed_seq_test",
    size = "small",
    srcs = ["salted_seed_seq_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":salted_seed_seq",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "chi_square_test",
    size = "small",
    srcs = [
        "chi_square_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":distribution_test_util",
        "//absl/base:core_headers",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "fast_uniform_bits_test",
    size = "small",
    srcs = [
        "fast_uniform_bits_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":fast_uniform_bits",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "mock_helpers",
    hdrs = ["mock_helpers.h"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:config",
        "//absl/base:fast_type_id",
        "//absl/types:optional",
    ],
)

cc_library(
    name = "mock_overload_set",
    testonly = True,
    hdrs = ["mock_overload_set.h"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":mock_helpers",
        "//absl/base:config",
        "//absl/random:mocking_bit_gen",
        "@googletest//:gtest",
    ],
)

cc_test(
    name = "nonsecure_base_test",
    size = "small",
    srcs = [
        "nonsecure_base_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":nonsecure_base",
        "//absl/meta:type_traits",
        "//absl/random",
        "//absl/random:distributions",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "seed_material_test",
    size = "small",
    srcs = ["seed_material_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":seed_material",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "pool_urbg_test",
    size = "small",
    srcs = [
        "pool_urbg_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":pool_urbg",
        "//absl/meta:type_traits",
        "//absl/types:span",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "pcg_engine_test",
    size = "medium",  # Trying to measure accuracy.
    srcs = ["pcg_engine_test.cc"],
    copts = ABSL_TEST_COPTS,
    flaky = 1,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":explicit_seed_seq",
        ":pcg_engine",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "randen_engine_test",
    size = "medium",
    srcs = [
        "randen_engine_test.cc",
    ],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":explicit_seed_seq",
        ":randen_engine",
        "//absl/log",
        "//absl/strings",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "randen_test",
    size = "small",
    srcs = ["randen_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":randen",
        "//absl/meta:type_traits",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "randen_slow_test",
    size = "small",
    srcs = ["randen_slow_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":platform",
        ":randen_slow",
        "//absl/base:endian",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "randen_hwaes_test",
    size = "small",
    srcs = ["randen_hwaes_test.cc"],
    copts = ABSL_TEST_COPTS + ABSL_RANDOM_RANDEN_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ABSL_RANDOM_NONPORTABLE_TAGS,
    deps = [
        ":platform",
        ":randen_hwaes",
        ":randen_hwaes_impl",  # build_cleaner: keep
        "//absl/log",
        "//absl/strings:str_format",
        "@googletest//:gtest",
    ],
)

cc_test(
    name = "wide_multiply_test",
    size = "small",
    srcs = ["wide_multiply_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":wide_multiply",
        "//absl/numeric:bits",
        "//absl/numeric:int128",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "nanobenchmark",
    srcs = ["nanobenchmark.cc"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    textual_hdrs = ["nanobenchmark.h"],
    deps = [
        ":platform",
        ":randen_engine",
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/base:raw_logging_internal",
    ],
)

cc_library(
    name = "uniform_helper",
    hdrs = ["uniform_helper.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":traits",
        "//absl/base:config",
        "//absl/meta:type_traits",
    ],
)

cc_library(
    name = "mock_validators",
    hdrs = ["mock_validators.h"],
    deps = [
        ":iostream_state_saver",
        ":uniform_helper",
        "//absl/base:config",
        "//absl/base:raw_logging_internal",
        "//absl/strings",
        "//absl/strings:string_view",
    ],
)

cc_test(
    name = "nanobenchmark_test",
    size = "small",
    srcs = ["nanobenchmark_test.cc"],
    flaky = 1,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "benchmark",
        "no_test_ios_x86_64",
        "no_test_loonix",  # Crashing.
        "no_test_wasm",
    ],
    deps = [
        ":nanobenchmark",
        "//absl/log",
        "//absl/log:check",
        "//absl/strings",
        "//absl/strings:str_format",
    ],
)

cc_test(
    name = "randen_benchmarks",
    size = "medium",
    timeout = "long",
    srcs = ["randen_benchmarks.cc"],
    copts = ABSL_TEST_COPTS + ABSL_RANDOM_RANDEN_COPTS,
    flaky = 1,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ABSL_RANDOM_NONPORTABLE_TAGS + ["benchmark"],
    deps = [
        ":nanobenchmark",
        ":platform",
        ":randen",
        ":randen_engine",
        ":randen_hwaes",
        ":randen_hwaes_impl",
        ":randen_slow",
        "//absl/base:raw_logging_internal",
        "//absl/strings",
    ],
)

cc_test(
    name = "iostream_state_saver_test",
    srcs = ["iostream_state_saver_test.cc"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":iostream_state_saver",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "uniform_helper_test",
    size = "small",
    srcs = ["uniform_helper_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":uniform_helper",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)
