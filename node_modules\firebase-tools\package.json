{"name": "firebase-tools", "version": "13.35.1", "description": "Command-Line Interface for Firebase", "main": "./lib/index.js", "bin": {"firebase": "./lib/bin/firebase.js"}, "files": ["lib", "schema", "standalone", "templates"], "repository": {"type": "git", "url": "https://github.com/firebase/firebase-tools.git"}, "keywords": ["cdn", "cli", "ssl", "cloud", "hosting", "firebase", "realtime", "websockets", "synchronization"], "preferGlobal": true, "engines": {"node": ">=18.0.0 || >=20.0.0 || >=22.0.0"}, "author": "Firebase (https://firebase.google.com/)", "license": "MIT", "bugs": {"url": "https://github.com/firebase/firebase-tools/issues"}, "homepage": "https://github.com/firebase/firebase-tools", "publishConfig": {"registry": "https://wombat-dressing-room.appspot.com"}, "nyc": {"require": ["ts-node/register"], "reporter": ["lcovonly", "text"], "report-dir": "./.coverage", "extension": [".js", ".ts"], "exclude": ["src/**/*.spec.*", "src/**/testing/**/*", "src/test/**/*"]}, "dependencies": {"@electric-sql/pglite": "^0.2.16", "@google-cloud/cloud-sql-connector": "^1.3.3", "@google-cloud/pubsub": "^4.5.0", "abort-controller": "^3.0.0", "ajv": "^8.17.1", "ajv-formats": "3.0.1", "archiver": "^7.0.0", "async-lock": "1.4.1", "body-parser": "^1.19.0", "chokidar": "^3.6.0", "cjson": "^0.3.1", "cli-table3": "0.6.5", "colorette": "^2.0.19", "commander": "^5.1.0", "configstore": "^5.0.1", "cors": "^2.8.5", "cross-env": "^7.0.3", "cross-spawn": "^7.0.5", "csv-parse": "^5.0.4", "deep-equal-in-any-order": "^2.0.6", "exegesis": "^4.2.0", "exegesis-express": "^4.0.0", "express": "^4.16.4", "filesize": "^6.1.0", "form-data": "^4.0.1", "fs-extra": "^10.1.0", "fuzzy": "^0.1.3", "gaxios": "^6.7.0", "glob": "^10.4.1", "google-auth-library": "^9.11.0", "inquirer": "^8.2.6", "inquirer-autocomplete-prompt": "^2.0.1", "js-yaml": "^3.14.1", "jsonwebtoken": "^9.0.0", "leven": "^3.1.0", "libsodium-wrappers": "^0.7.10", "lodash": "^4.17.21", "lsofi": "1.0.0", "marked": "^13.0.2", "marked-terminal": "^7.0.0", "mime": "^2.5.2", "minimatch": "^3.0.4", "morgan": "^1.10.0", "node-fetch": "^2.6.7", "open": "^6.3.0", "ora": "^5.4.1", "p-limit": "^3.0.1", "pg": "^8.11.3", "portfinder": "^1.0.32", "progress": "^2.0.3", "proxy-agent": "^6.3.0", "retry": "^0.13.1", "semver": "^7.5.2", "sql-formatter": "^15.3.0", "stream-chain": "^2.2.4", "stream-json": "^1.7.3", "superstatic": "^9.2.0", "tar": "^6.1.11", "tcp-port-used": "^1.0.2", "tmp": "^0.2.3", "triple-beam": "^1.3.0", "universal-analytics": "^0.5.3", "update-notifier-cjs": "^5.1.6", "uuid": "^8.3.2", "winston": "^3.0.0", "winston-transport": "^4.4.0", "ws": "^7.5.10", "yaml": "^2.4.1"}, "overrides": {"@angular-devkit/core": {"ajv-formats": "3.0.1", "ajv": "^8.17.1"}, "node-fetch": {"whatwg-url": "^14.0.0"}}}