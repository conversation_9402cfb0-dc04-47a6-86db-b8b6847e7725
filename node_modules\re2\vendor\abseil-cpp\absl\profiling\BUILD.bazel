# Copyright 2021 The Abseil Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_DEFAULT_LINKOPTS",
    "ABSL_TEST_COPTS",
)

package(
    default_visibility = ["//visibility:private"],
    features = [
        "header_modules",
        "layering_check",
        "parse_headers",
    ],
)

licenses(["notice"])

cc_library(
    name = "sample_recorder",
    hdrs = ["internal/sample_recorder.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//absl:__subpackages__",
    ],
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
        "//absl/synchronization",
        "//absl/time",
    ],
)

cc_test(
    name = "sample_recorder_test",
    srcs = ["internal/sample_recorder_test.cc"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = [
        "no_test_wasm",
    ],
    deps = [
        ":sample_recorder",
        "//absl/base:core_headers",
        "//absl/synchronization",
        "//absl/synchronization:thread_pool",
        "//absl/time",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "exponential_biased",
    srcs = ["internal/exponential_biased.cc"],
    hdrs = ["internal/exponential_biased.h"],
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        "//absl:__subpackages__",
    ],
    deps = [
        "//absl/base:config",
        "//absl/base:core_headers",
    ],
)

cc_test(
    name = "exponential_biased_test",
    size = "small",
    srcs = ["internal/exponential_biased_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":exponential_biased",
        "//absl/strings",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "periodic_sampler",
    srcs = ["internal/periodic_sampler.cc"],
    hdrs = ["internal/periodic_sampler.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = [
        # TODO(b/304670045): remove after periodic_sampler moves to //spanner/common.
        "//absl:__subpackages__",
    ],
    deps = [
        ":exponential_biased",
        "//absl/base:core_headers",
    ],
)

cc_test(
    name = "periodic_sampler_test",
    size = "small",
    srcs = ["internal/periodic_sampler_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    visibility = ["//visibility:private"],
    deps = [
        ":periodic_sampler",
        "//absl/base:core_headers",
        "@googletest//:gtest",
        "@googletest//:gtest_main",
    ],
)

cc_binary(
    name = "periodic_sampler_benchmark",
    testonly = True,
    srcs = ["internal/periodic_sampler_benchmark.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":periodic_sampler",
        "//absl/base:core_headers",
        "@google_benchmark//:benchmark_main",
    ],
)
