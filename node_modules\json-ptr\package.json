{"name": "json-ptr", "version": "3.1.1", "author": "<PERSON> <<EMAIL>>", "description": "A complete implementation of JSON Pointer (RFC 6901) for nodejs and modern browsers.", "keywords": ["6901", "json", "pointers", "fragmentid"], "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "umd": "dist/json-ptr.min.js", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "types": "dist/types/index.d.ts", "files": ["dist"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/flitbit/json-ptr"}, "scripts": {"clean": "shx rm -fr coverage dist dist.browser docs tmp .nyc_output", "prebuild": "npm run lint", "prebuild:browser": "shx rm -fr dist.browser", "build:browser": "webpack", "build:browser-tests": "webpack --config webpack.tests.config.js", "buildall": "npm run build-lib && npm run build:browser && npm run build:browser-tests && npm run docs", "build-lib": "rollup -c", "build": "npm run buildall", "prebuild-lib": "shx rm -fr dist", "postbuild-lib": "shx cp package-cjs.json dist/cjs/package.json && shx cp package-esm.json dist/esm/package.json", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "test": "cross-env TS_NODE_COMPILER_OPTIONS='{ \"module\": \"commonjs\", \"noEmit\": false }' nyc mocha src/**/*.spec.ts", "test:watch": "onchange --initial \"*.js\" \"*.json\" \"src/**/*.ts\" -- npm run test", "cilint": "eslint . --ext .ts,.tsx --format junit --output-file ./reports/eslint/eslint.xml", "precibuild": "npm run cilint", "cibuild": "npm run buildall", "preci": "npm run cibuild", "ci": "cross-env TS_NODE_COMPILER_OPTIONS='{ \"module\": \"commonjs\", \"noEmit\": false }' nyc mocha src/**/*.spec.ts --timeout=10000 --exit --reporter mocha-junit-reporter --reporter-options mochaFile=reports/mocha/test-results.xml", "predocs": "shx rm -fr docs", "docs": "typedoc src/index.ts && shx cp docs-overlay/* docs/"}, "devDependencies": {"@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.1.3", "@rollup/plugin-typescript": "^8.3.0", "@types/assert-plus": "^1.0.4", "@types/bent": "^7.3.2", "@types/chai": "^4.3.0", "@types/debug": "^4.1.7", "@types/mocha": "^9.1.0", "@types/node": "^17.0.14", "@typescript-eslint/eslint-plugin": "^5.10.2", "@typescript-eslint/parser": "^5.10.2", "bent": "^7.3.12", "chai": "^4.3.6", "cross-env": "^7.0.3", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-mocha": "^10.0.3", "eslint-plugin-prettier": "^4.0.0", "mocha": "^9.2.0", "mocha-junit-reporter": "^2.0.2", "nyc": "^15.1.0", "onchange": "^7.1.0", "prettier": "^2.5.1", "process": "^0.11.10", "rimraf": "~3.0.2", "rollup": "^2.67.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "shx": "^0.3.4", "terser-webpack-plugin": "^5.3.1", "ts-loader": "^9.2.6", "ts-node": "^10.4.0", "typedoc": "^0.22.11", "typescript": "^4.5.5", "util": "^0.12.4", "uuid": "^7.0.3", "webpack": "^5.68.0", "webpack-cli": "^4.9.2"}, "nyc": {"include": ["src/**/*.ts"], "extension": [".ts", ".tsx"], "require": ["ts-node/register"], "reporter": ["text-summary", "html"], "sourceMap": true, "instrument": true}}