{"name": "nearley", "version": "2.20.1", "description": "Simple, fast, powerful parser toolkit for JavaScript.", "main": "lib/nearley.js", "dependencies": {"commander": "^2.19.0", "moo": "^0.5.0", "railroad-diagrams": "^1.0.0", "randexp": "0.4.6"}, "files": ["bin/", "lib/", "builtin/"], "bin": {"nearleyc": "bin/nearleyc.js", "nearley-test": "bin/nearley-test.js", "nearley-unparse": "bin/nearley-unparse.js", "nearley-railroad": "bin/nearley-railroad.js"}, "keywords": ["parser", "parse", "generator", "compiler", "compile", "grammar", "language"], "scripts": {"bootstrap": "mocha test/bootstrap.test.js && bin/nearleyc.js lib/nearley-language-bootstrapped.ne > tmp && mv tmp lib/nearley-language-bootstrapped.js && echo bootstrapped ok", "benchmark": "benchr test/benchmark.js", "test": "mocha test/*.test.js", "doctoc": "doctoc --notitle README.md", "profile": "bin/nearleyc.js test/grammars/parens.ne > test/grammars/parens.js && node test/profile.js"}, "author": "Hardmath123", "contributors": "https://github.com/Hardmath123/nearley/graphs/contributors", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/hardmath123/nearley.git"}, "devDependencies": {"@types/moo": "^0.3.0", "@types/node": "^7.0.27", "babel-cli": "^6.18.0", "babel-preset-env": "^1.6.0", "benchr": "^3.2.0", "coffee-script": "^1.10.0", "doctoc": "^1.3.0", "expect": "^1.20.2", "microtime": "^2.1.2", "mocha": "^8.2.1", "typescript": "^2.6.1"}, "funding": {"type": "individual", "url": "https://nearley.js.org/#give-to-nearley"}}