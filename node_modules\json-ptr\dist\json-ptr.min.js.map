{"version": 3, "file": "json-ptr.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,cAAe,GAAIH,GACA,iBAAZC,QACdA,QAAqB,YAAID,IAEzBD,EAAkB,YAAIC,IARxB,CASGK,MAAM,WACT,M,mBCTA,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3EF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFT,EAAyBL,IACH,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,M,KCKhD,SAASC,EAAQC,EAAgBC,EAAcC,GAKpD,IAJA,IAAIC,EAAM,GACNC,EAAMJ,EACNK,EAAM,EACNC,GAAO,GACHA,EAAMF,EAAIG,QAAQN,KAAU,GAClCE,GAAOH,EAAOQ,UAAUH,EAAKA,EAAMC,GAAOJ,EAC1CE,EAAMA,EAAII,UAAUF,EAAML,EAAKQ,OAAQL,EAAIK,QAC3CJ,GAAOC,EAAML,EAAKQ,OAKpB,OAHIL,EAAIK,OAAS,IACfN,GAAOH,EAAOQ,UAAUR,EAAOS,OAASL,EAAIK,OAAQT,EAAOS,SAEtDN,EAGF,SAASO,EAAuBC,GAIrC,IAHA,IAAIC,GAAK,EACHC,EAAMF,EAASF,OACfN,EAAM,IAAIW,MAAMD,KACbD,EAAIC,GACgB,iBAAhBF,EAASC,GAClBT,EAAIS,GAAKb,EACPA,EAAQgB,mBAAmBJ,EAASC,IAAe,KAAM,KACzD,KACA,KAGFT,EAAIS,GAAKD,EAASC,GAGtB,OAAOT,EAGF,SAASa,EAAuBL,GAIrC,IAHA,IAAIC,GAAK,EACHC,EAAMF,EAASF,OACfN,EAAM,IAAIW,MAAMD,KACbD,EAAIC,GACgB,iBAAhBF,EAASC,GAClBT,EAAIS,GAAKK,mBACPlB,EAAQA,EAAQY,EAASC,GAAc,IAAK,MAAO,IAAK,OAG1DT,EAAIS,GAAKD,EAASC,GAGtB,OAAOT,EAGF,SAASe,EAAsBP,GAIpC,IAHA,IAAIC,GAAK,EACHC,EAAMF,EAASF,OACfN,EAAM,IAAIW,MAAMD,KACbD,EAAIC,GACgB,iBAAhBF,EAASC,GAClBT,EAAIS,GAAKb,EAAQA,EAAQY,EAASC,GAAc,KAAM,KAAM,KAAM,KAElET,EAAIS,GAAKD,EAASC,GAGtB,OAAOT,EAGF,SAASgB,EAAsBR,GAIpC,IAHA,IAAIC,GAAK,EACHC,EAAMF,EAASF,OACfN,EAAM,IAAIW,MAAMD,KACbD,EAAIC,GACgB,iBAAhBF,EAASC,GAClBT,EAAIS,GAAKb,EAAQA,EAAQY,EAASC,GAAc,IAAK,MAAO,IAAK,MAEjET,EAAIS,GAAKD,EAASC,GAGtB,OAAOT,EAGF,SAASiB,EAAcC,GAC5B,GAAmB,iBAARA,EACT,MAAM,IAAIC,UACR,2DAGJ,GAAmB,IAAfD,EAAIZ,OACN,MAAO,GAET,GAAe,MAAXY,EAAI,GACN,MAAM,IAAIE,eACR,iFAGJ,OAAOL,EAAsBG,EAAIb,UAAU,GAAGgB,MAAM,MAG/C,SAASC,EAAcC,GAC5B,IAAKA,GAASA,IAASZ,MAAMa,QAAQD,GACnC,MAAM,IAAIJ,UAAU,oDAEtB,OAAoB,IAAhBI,EAAKjB,OACA,GAEF,IAAImB,OAAOT,EAAsBO,GAAMG,KAAK,MAG9C,SAASC,EACdT,GAEA,GAAmB,iBAARA,EACT,MAAM,IAAIC,UACR,2DAGJ,GAAmB,IAAfD,EAAIZ,QAA2B,MAAXY,EAAI,GAC1B,MAAM,IAAIE,eACR,iFAGJ,GAAmB,IAAfF,EAAIZ,OACN,MAAO,GAET,GAAe,MAAXY,EAAI,GACN,MAAM,IAAIE,eAAe,gCAE3B,OAAOb,EAAuBW,EAAIb,UAAU,GAAGgB,MAAM,MAGhD,SAASO,EACdL,GAEA,IAAKA,GAASA,IAASZ,MAAMa,QAAQD,GACnC,MAAM,IAAIJ,UAAU,oDAEtB,OAAoB,IAAhBI,EAAKjB,OACA,IAEF,KAAKmB,OAAOZ,EAAuBU,GAAMG,KAAK,M,oeAGvD,IAAMG,EACJ,4JAEK,SAASC,EAAsBZ,GACpC,GAAmB,iBAARA,EACT,MAAM,IAAIC,UACR,oEAGJ,GAAmB,IAAfD,EAAIZ,OAEN,MAAM,IAAIc,eAAeS,GAE3B,IAAMrB,EAAWU,EAAIG,MAAM,KACvBU,EAAQvB,EAAS,GAErB,GAA+B,KAA3BuB,EAAMA,EAAMzB,OAAS,GAAW,CAClC,GAAIE,EAASF,OAAS,EACpB,MAAM,IAAIc,eAAeS,GAE3BE,EAAQA,EAAMC,OAAO,EAAGD,EAAMzB,OAAS,GAIzC,IAFA,IAAIG,GAAK,EACHC,EAAMqB,EAAMzB,SACTG,EAAIC,GACX,GAAIqB,EAAMtB,GAAK,KAAOsB,EAAMtB,GAAK,IAC/B,MAAM,IAAIW,eAAeS,GAG7B,IAAMN,EAAkBR,EAAsBP,EAASyB,MAAM,IAE7D,OADAV,EAAKW,QAAQ1B,EAAS,IACfe,EAGF,SAASY,EACdC,EACAC,GAEA,GAAmB,iBAARA,EAAkB,OAAOA,EACpC,IAAM3B,EAAM2B,EAAI/B,OAChB,IAAKI,EAAK,OAAQ,EAClB,IAAI4B,EAAS,EACb,GAAY,IAAR5B,GAAwB,MAAX2B,EAAI,GACnB,OAAK1B,MAAMa,QAAQY,GAGZA,EAAI9B,OAFF,EAIX,OAASgC,EAAS5B,GAChB,GAAI2B,EAAIC,GAAU,KAAOD,EAAIC,GAAU,IACrC,OAAQ,EAGZ,OAAOC,SAASF,EAAK,IAKhB,SAASG,EAA0BjB,GACxC,IAAIkB,EAAO,iCACX,OAAoB,IAAhBlB,EAAKjB,OACA,SAACoC,GAAgB,OAAAA,IAE1BD,EAAOlB,EAAKoB,QAAO,SAACF,EAAMG,EAAGnC,GAC3B,OACEgC,EACA,0CACA7C,EAAQA,EAAQ2B,EAAKd,GAAK,GAAI,KAAM,QAAS,IAAK,OAClD,yBAED,kCACHgC,GAAc,wBAEP,IAAII,SAAS,KAAMJ,IAGrB,SAASK,EACdC,EACAC,EACAzB,EACA0B,GAEA,QAFA,IAAAA,IAAAA,GAAA,GAEoB,IAAhB1B,EAAKjB,OACP,MAAM,IAAI4C,MAAM,mDAElB,QAAsB,IAAXH,EACT,MAAM,IAAI5B,UAAU,kCAUtB,IAPA,IAGIgC,EAEAlD,EACAmD,EANAV,EAAUK,EACRrC,EAAMa,EAAKjB,OACXH,EAAMoB,EAAKjB,OAAS,EAEtBgC,GAAU,IAGLA,EAAS5B,GAAK,CAErB,GAAoB,iBADpByC,EAAO5B,EAAKe,KACoC,iBAATa,EACrC,MAAM,IAAIhC,UAAU,8CAEtB,GAIW,cAATgC,GACS,gBAATA,GACS,cAATA,EAEA,MAAM,IAAID,MAAM,6CAElB,GAAIvC,MAAMa,QAAQkB,GAAK,CACrB,GAAa,MAATS,GAAgBb,IAAWnC,EAE7B,YADAuC,EAAGW,KAAKL,GAIV,GADAI,EAAIjB,EAAsBO,EAAIS,GAC1BT,EAAGpC,OAAS8C,EAAG,CACjB,GAAId,IAAWnC,EAAK,CAClBF,EAAMyC,EAAGU,GACTV,EAAGU,GAAKJ,EACR,MAEFN,EAAKA,EAAGU,QACH,GAAId,IAAWnC,GAAOiD,IAAMV,EAAGpC,QACpC,GAAI2C,EAEF,YADAP,EAAGW,KAAKL,QAGDC,IACTP,EAAKA,EAAGU,GAAKd,IAAWnC,EAAM6C,EAAM,QAEjC,CACL,QAAwB,IAAbN,EAAGS,GAAuB,CACnC,GAAIF,EAAO,CACT,GAAIX,IAAWnC,EAEb,YADAuC,EAAGS,GAAQH,GAIb,IAAMM,EAAIC,OAAOhC,EAAKe,EAAS,IAC/B,GACEiB,OAAOC,UAAUF,KACuB,IAAxCnB,EAAsBO,EAAGS,GAAOG,GAChC,CACAZ,EAAKA,EAAGS,GAAQ,GAChB,SAEFT,EAAKA,EAAGS,GAAQ,GAChB,SAEF,OAEF,GAAIb,IAAWnC,EAAK,CAClBF,EAAMyC,EAAGS,GACTT,EAAGS,GAAQH,EACX,MAEFN,EAAKA,EAAGS,IAGZ,OAAOlD,EAGF,SAASwD,EAAiBV,EAAiBxB,GAChD,GAAoB,IAAhBA,EAAKjB,OACP,MAAM,IAAI4C,MAAM,qDAElB,QAAsB,IAAXH,EACT,MAAM,IAAI5B,UAAU,oCAUtB,IAPA,IAGIgC,EAEAlD,EACAmD,EANAV,EAAUK,EACRrC,EAAMa,EAAKjB,OACXH,EAAMoB,EAAKjB,OAAS,EAEtBgC,GAAU,IAGLA,EAAS5B,GAAK,CAErB,GAAoB,iBADpByC,EAAO5B,EAAKe,KACoC,iBAATa,EACrC,MAAM,IAAIhC,UAAU,8CAEtB,GACW,cAATgC,GACS,gBAATA,GACS,cAATA,EAEA,MAAM,IAAID,MAAM,6CAElB,GAAIvC,MAAMa,QAAQkB,GAAK,CAErB,IADAU,EAAIjB,EAAsBO,EAAIS,KACrBT,EAAGpC,OAAQ,OACpB,GAAIgC,IAAWnC,EAAK,CAClBF,EAAMyC,EAAGU,UACFV,EAAGU,GACV,MAEFV,EAAKA,EAAGU,OACH,CACL,QAAwB,IAAbV,EAAGS,GACZ,OAEF,GAAIb,IAAWnC,EAAK,CAClBF,EAAMyC,EAAGS,UACFT,EAAGS,GACV,MAEFT,EAAKA,EAAGS,IAGZ,OAAOlD,EAGF,SAASyD,EAAkBxC,GAChC,MAAsB,iBAARA,GAAoBA,EAAIZ,OAAS,GAAgB,MAAXY,EAAI,GAGnD,SAASyC,EAAYzC,GAC1B,OAAOwC,EAAkBxC,GAAOS,EAA8BV,EAGzD,SAAS2C,EAAc1C,GAC5B,OAAOP,MAAMa,QAAQN,GACjBA,EAAIe,MAAM,GACV0B,EAAYzC,EAAZyC,CAA4BzC,GC1VlC,SAAS2C,EAASlE,GAChB,MAAwB,iBAAVA,GAAgC,OAAVA,EAiBtC,SAASmE,EAAc1E,GACrB,OAAOyE,EAASzE,KAAS2E,EAAcC,YAAY5E,GAGrD,SAAS6E,EACPlB,EACAmB,EACAC,GAIA,IAFA,IAAMC,EAAkB,IAAIC,IACtBC,EAAY,CAAC,CAAElF,IAAK2D,EAAQxB,KAAM,KACjC+C,EAAEhE,QAAQ,CACT,MAAgBgE,EAAEC,QAAhBnF,EAAG,MAAEmC,EAAI,OAEjB,GADA2C,EAAQC,EAAQ5C,GAAOnC,GACnB0E,EAAc1E,GAKhB,GAJAgF,EAAgBI,IACdpF,EACA,IAAIqF,EAAY7C,EAA4BL,KAEzCZ,MAAMa,QAAQpC,GAoBjB,KAAIsF,GAAK,EAET,IADMhE,EAAMtB,EAAIkB,SACPoE,EAAIhE,GAAK,CAChB,IAAM,EAAKtB,EAAIsF,GACXb,EAAS,IAAOO,EAAgBO,IAAI,GACtCL,EAAEjB,KAAK,CACLjE,IAAK,IAAI2E,EAAcK,EAAgBjF,IAAI,IAC3CoC,KAAMA,EAAKE,OAAO,CAACiD,EAAI,OAGzBJ,EAAEjB,KAAK,CACLjE,IAAK,EACLmC,KAAMA,EAAKE,OAAO,CAACiD,EAAI,aA5B7B,IAHA,IAAME,EAAO5F,OAAO4F,KAAKxF,GACnBsB,EAAMkE,EAAKtE,OACbG,GAAK,IACAA,EAAIC,GAAK,CAChB,IAAM,EAAMtB,EAAgCwF,EAAKnE,IAC7CoD,EAAS,IAAOO,EAAgBO,IAAI,GACtCL,EAAEjB,KAAK,CACLjE,IAAK,IAAI2E,EAAcK,EAAgBjF,IAAI,IAC3CoC,KAAMA,EAAKE,OAAOmD,EAAKnE,MAGzB6D,EAAEjB,KAAK,CACLjE,IAAK,EACLmC,KAAMA,EAAKE,OAAOmD,EAAKnE,QA4BrC,IAAMoE,EAAOpF,OAAO,WAEdqF,EAAOrF,OAAO,cAEdsF,EAAOtF,OAAO,UA6GpB,aA8QE,WAAYyB,GACV8D,KAAKzD,KAAOqC,EAAc1C,GA8I9B,OAzYS,EAAA+D,OAAP,SAAcC,GACZ,OAAO,IAAIT,EAAYS,IAsBlB,EAAAP,IAAP,SACE5B,EACAmC,GAKA,OAHuB,iBAAZA,GAAwBvE,MAAMa,QAAQ0D,MAC/CA,EAAU,IAAIT,EAAYS,IAEpBA,EAAwBP,IAAI5B,IAsB/B,EAAA5D,IAAP,SACE4D,EACAmC,GAKA,OAHuB,iBAAZA,GAAwBvE,MAAMa,QAAQ0D,MAC/CA,EAAU,IAAIT,EAAYS,IAEpBA,EAAwB/F,IAAI4D,IAuC/B,EAAAyB,IAAP,SACEzB,EACAmC,EACAlC,EACAC,GAKA,YALA,IAAAA,IAAAA,GAAA,IAEuB,iBAAZiC,GAAwBvE,MAAMa,QAAQ0D,MAC/CA,EAAU,IAAIT,EAAYS,IAEpBA,EAAwBV,IAAIzB,EAAQC,EAAKC,IAiC5C,EAAAkC,MAAP,SACEpC,EACAmC,GAKA,OAHuB,iBAAZA,GAAwBvE,MAAMa,QAAQ0D,MAC/CA,EAAU,IAAIT,EAAYS,IAEpBA,EAAwBC,MAAMpC,IAOjC,EAAAqC,OAAP,SAAcF,GACZ,OAAOvB,EAAYuB,EAAZvB,CAAqBuB,IASvB,EAAAG,MAAP,SAAatC,EAAiBmB,EAAkBoB,QAAA,IAAAA,IAAAA,GAAA,GAC9CrB,EACElB,EACAmB,EACAoB,EAAa1D,EAA8BN,IAQxC,EAAAiE,aAAP,SAAoBxC,GAClB,IAAM/C,EAAmC,GAQzC,OAPAiE,EACElB,GACA,SAACmC,EAASvF,GACRK,EAAIqD,KAAK,CAAE6B,QAAO,EAAEvF,MAAK,MAE3B2B,GAEKtB,GAOF,EAAAwF,gBAAP,SACEzC,GAEA,IAAM/C,EAA8C,GAQpD,OAPAiE,EACElB,GACA,SAACuC,EAAY3F,GACXK,EAAIqD,KAAK,CAAEiC,WAAU,EAAE3F,MAAK,MAE9BiC,GAEK5B,GAQF,EAAAyF,QAAP,SACE1C,EACAuC,QAAA,IAAAA,IAAAA,GAAA,GAEA,IAAMtF,EAAgC,GAQtC,OAPAiE,EACElB,GACA,SAACK,EAAGsC,GACF1F,EAAIoD,GAAKsC,IAEXJ,EAAa1D,EAA8BN,GAEtCtB,GAQF,EAAA2F,IAAP,SAAW5C,EAAiBuC,QAAA,IAAAA,IAAAA,GAAA,GAC1B,IAAMtF,EAAM,IAAIqE,IAMhB,OALAJ,EACElB,EACA/C,EAAIwE,IAAIoB,KAAK5F,GACbsF,EAAa1D,EAA8BN,GAEtCtB,GAoBT,YAAAb,IAAA,SAAI4D,GAIF,OAHKiC,KAAKD,KACRC,KAAKD,GAAQvC,EAA0BwC,KAAKzD,OAEvCyD,KAAKD,GAAMhC,IAcpB,YAAAyB,IAAA,SAAIzB,EAAiBpD,EAAgBsD,GACnC,YADmC,IAAAA,IAAAA,GAAA,GAC5BH,EAAeC,EAAQpD,EAAOqF,KAAKzD,KAAM0B,IASlD,YAAAkC,MAAA,SAAMpC,GACJ,OAAOU,EAAiBV,EAAQiC,KAAKzD,OAOvC,YAAAoD,IAAA,SAAI5B,GACF,YAAmC,IAArBiC,KAAK7F,IAAI4D,IAOzB,YAAA8C,OAAA,SAAO9C,GACL,IAAMK,EAAI4B,KAAKzD,KACf,GAAgB,GAAZ6B,EAAE9C,OAEN,OADe,IAAImE,EAAYrB,EAAEnB,MAAM,EAAGmB,EAAE9C,OAAS,IACvCnB,IAAI4D,IAQpB,YAAA+C,SAAA,SAAS5E,GACP,IAAMkC,EAAI4B,KAAKzD,KACTwE,EAAUjE,EAAsBZ,GAChCoC,EAAIf,SAASwD,EAAQ,IAC3B,GAAIzC,EAAIF,EAAE9C,OAAQ,MAAM,IAAI4C,MAAM,qCAClC,IAAM8C,EAAI5C,EAAEnB,MAAM,EAAGmB,EAAE9C,OAASgD,GAAG7B,OAAOsE,EAAQ9D,MAAM,IACxD,GAAyC,KAArC8D,EAAQ,GAAGA,EAAQ,GAAGzF,OAAS,GAAW,CAE5C,IAAM,EAAO0F,EAAEA,EAAE1F,OAAS,GAC1B,MAAM,IAAI4C,MACR,8DAAuD,EAAI,iDAG/D,OAAO,IAAIuB,EAAYuB,IASzB,YAAAC,IAAA,SAAIlD,EAAiB7B,GACnB,IAAMkC,EAAI4B,KAAKzD,KACTwE,EAAUjE,EAAsBZ,GAChCoC,EAAIf,SAASwD,EAAQ,IAC3B,KAAIzC,EAAIF,EAAE9C,QAAV,CAIA,IAAM0F,EAAI5C,EAAEnB,MAAM,EAAGmB,EAAE9C,OAASgD,GAAG7B,OAAOsE,EAAQ9D,MAAM,IAClDiE,EAAQ,IAAIzB,EAAYuB,GAC9B,GAAyC,KAArCD,EAAQ,GAAGA,EAAQ,GAAGzF,OAAS,GAAW,CAE5C,IAAM,EAAO0F,EAAEA,EAAE1F,OAAS,GACpB,EAAS4F,EAAML,OAAO9C,GAC5B,OAAOpC,MAAMa,QAAQ,GAAUe,SAAS,EAAM,IAAM,EAEtD,OAAO2D,EAAM/G,IAAI4D,KAOnB,YAAAtB,OAAA,SAAOP,GACL,OAAO,IAAIuD,EACTO,KAAKzD,KAAKE,OACRP,aAAeuD,EAAcvD,EAAIK,KAAOqC,EAAc1C,MAQ5D,sBAAI,sBAAO,C,IAAX,WAIE,YAHmBiF,IAAfnB,KAAKH,KACPG,KAAKH,GAAQvD,EAAc0D,KAAKzD,OAE3ByD,KAAKH,I,gCAMd,sBAAI,oCAAqB,C,IAAzB,WAIE,OAHKG,KAAKF,KACRE,KAAKF,GAAQlD,EAA4BoD,KAAKzD,OAEzCyD,KAAKF,I,gCAMd,YAAAsB,SAAA,WACE,OAAOpB,KAAKE,SAEhB,EA7ZA,GAgaMmB,EAAW5G,OAAO,WAQxB,aAuBE,WAAYyF,GACVF,KAAKqB,GACHnB,aAAmBT,EAAcS,EAAU,IAAIT,EAAYS,GAC7DF,KAAKsB,KAAOtB,KAAKqB,GAAUE,sBAyB/B,OA9CS,EAAAvC,YAAP,SAAmBwC,GACjB,IAAKA,EAAW,OAAO,EACvB,IAAMC,EAAMD,EACZ,MAA2B,iBAAbC,EAAIH,MAA4C,mBAAhBG,EAAIC,SA0BpD,YAAAA,QAAA,SAAQ3D,GACN,OAAOiC,KAAKqB,GAAUlH,IAAI4D,IAM5B,YAAAmC,QAAA,WACE,OAAOF,KAAKqB,IAMd,YAAAD,SAAA,WACE,OAAOpB,KAAKsB,MAEhB,EAnDA,G", "sources": ["webpack://JsonPointer/webpack/universalModuleDefinition", "webpack://JsonPointer/webpack/bootstrap", "webpack://JsonPointer/webpack/runtime/define property getters", "webpack://JsonPointer/webpack/runtime/hasOwnProperty shorthand", "webpack://JsonPointer/webpack/runtime/make namespace object", "webpack://JsonPointer/./src/util.ts", "webpack://JsonPointer/./src/pointer.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"JsonPointer\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"JsonPointer\"] = factory();\n\telse\n\t\troot[\"JsonPointer\"] = factory();\n})(self, function() {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>ointer,\n  UriFragment<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  RelativeJsonPointer,\n  PathSegment,\n  PathSegments,\n  Decoder,\n} from './types';\n\nexport function replace(source: string, find: string, repl: string): string {\n  let res = '';\n  let rem = source;\n  let beg = 0;\n  let end = -1;\n  while ((end = rem.indexOf(find)) > -1) {\n    res += source.substring(beg, beg + end) + repl;\n    rem = rem.substring(end + find.length, rem.length);\n    beg += end + find.length;\n  }\n  if (rem.length > 0) {\n    res += source.substring(source.length - rem.length, source.length);\n  }\n  return res;\n}\n\nexport function decodeFragmentSegments(segments: PathSegments): PathSegments {\n  let i = -1;\n  const len = segments.length;\n  const res = new Array(len);\n  while (++i < len) {\n    if (typeof segments[i] === 'string') {\n      res[i] = replace(\n        replace(decodeURIComponent(segments[i] as string), '~1', '/'),\n        '~0',\n        '~',\n      );\n    } else {\n      res[i] = segments[i];\n    }\n  }\n  return res;\n}\n\nexport function encodeFragmentSegments(segments: PathSegments): PathSegments {\n  let i = -1;\n  const len = segments.length;\n  const res = new Array(len);\n  while (++i < len) {\n    if (typeof segments[i] === 'string') {\n      res[i] = encodeURIComponent(\n        replace(replace(segments[i] as string, '~', '~0'), '/', '~1'),\n      );\n    } else {\n      res[i] = segments[i];\n    }\n  }\n  return res;\n}\n\nexport function decodePointerSegments(segments: PathSegments): PathSegments {\n  let i = -1;\n  const len = segments.length;\n  const res = new Array(len);\n  while (++i < len) {\n    if (typeof segments[i] === 'string') {\n      res[i] = replace(replace(segments[i] as string, '~1', '/'), '~0', '~');\n    } else {\n      res[i] = segments[i];\n    }\n  }\n  return res;\n}\n\nexport function encodePointerSegments(segments: PathSegments): PathSegments {\n  let i = -1;\n  const len = segments.length;\n  const res = new Array(len);\n  while (++i < len) {\n    if (typeof segments[i] === 'string') {\n      res[i] = replace(replace(segments[i] as string, '~', '~0'), '/', '~1');\n    } else {\n      res[i] = segments[i];\n    }\n  }\n  return res;\n}\n\nexport function decodePointer(ptr: Pointer): PathSegments {\n  if (typeof ptr !== 'string') {\n    throw new TypeError(\n      'Invalid type: JSON Pointers are represented as strings.',\n    );\n  }\n  if (ptr.length === 0) {\n    return [];\n  }\n  if (ptr[0] !== '/') {\n    throw new ReferenceError(\n      'Invalid JSON Pointer syntax. Non-empty pointer must begin with a solidus `/`.',\n    );\n  }\n  return decodePointerSegments(ptr.substring(1).split('/'));\n}\n\nexport function encodePointer(path: PathSegments): JsonStringPointer {\n  if (!path || (path && !Array.isArray(path))) {\n    throw new TypeError('Invalid type: path must be an array of segments.');\n  }\n  if (path.length === 0) {\n    return '';\n  }\n  return '/'.concat(encodePointerSegments(path).join('/'));\n}\n\nexport function decodeUriFragmentIdentifier(\n  ptr: UriFragmentIdentifierPointer,\n): PathSegments {\n  if (typeof ptr !== 'string') {\n    throw new TypeError(\n      'Invalid type: JSON Pointers are represented as strings.',\n    );\n  }\n  if (ptr.length === 0 || ptr[0] !== '#') {\n    throw new ReferenceError(\n      'Invalid JSON Pointer syntax; URI fragment identifiers must begin with a hash.',\n    );\n  }\n  if (ptr.length === 1) {\n    return [];\n  }\n  if (ptr[1] !== '/') {\n    throw new ReferenceError('Invalid JSON Pointer syntax.');\n  }\n  return decodeFragmentSegments(ptr.substring(2).split('/'));\n}\n\nexport function encodeUriFragmentIdentifier(\n  path: PathSegments,\n): UriFragmentIdentifierPointer {\n  if (!path || (path && !Array.isArray(path))) {\n    throw new TypeError('Invalid type: path must be an array of segments.');\n  }\n  if (path.length === 0) {\n    return '#';\n  }\n  return '#/'.concat(encodeFragmentSegments(path).join('/'));\n}\n\nconst InvalidRelativePointerError =\n  'Invalid Relative JSON Pointer syntax. Relative pointer must begin with a non-negative integer, followed by either the number sign (#), or a JSON Pointer.';\n\nexport function decodeRelativePointer(ptr: RelativeJsonPointer): PathSegments {\n  if (typeof ptr !== 'string') {\n    throw new TypeError(\n      'Invalid type: Relative JSON Pointers are represented as strings.',\n    );\n  }\n  if (ptr.length === 0) {\n    // https://tools.ietf.org/id/draft-handrews-relative-json-pointer-00.html#rfc.section.3\n    throw new ReferenceError(InvalidRelativePointerError);\n  }\n  const segments = ptr.split('/');\n  let first = segments[0];\n  // It is a name reference; strip the hash.\n  if (first[first.length - 1] == '#') {\n    if (segments.length > 1) {\n      throw new ReferenceError(InvalidRelativePointerError);\n    }\n    first = first.substr(0, first.length - 1);\n  }\n  let i = -1;\n  const len = first.length;\n  while (++i < len) {\n    if (first[i] < '0' || first[i] > '9') {\n      throw new ReferenceError(InvalidRelativePointerError);\n    }\n  }\n  const path: unknown[] = decodePointerSegments(segments.slice(1));\n  path.unshift(segments[0]);\n  return path as PathSegments;\n}\n\nexport function toArrayIndexReference(\n  arr: readonly unknown[],\n  idx: PathSegment,\n): number {\n  if (typeof idx === 'number') return idx;\n  const len = idx.length;\n  if (!len) return -1;\n  let cursor = 0;\n  if (len === 1 && idx[0] === '-') {\n    if (!Array.isArray(arr)) {\n      return 0;\n    }\n    return arr.length;\n  }\n  while (++cursor < len) {\n    if (idx[cursor] < '0' || idx[cursor] > '9') {\n      return -1;\n    }\n  }\n  return parseInt(idx, 10);\n}\n\nexport type Dereference = (it: unknown) => unknown;\n\nexport function compilePointerDereference(path: PathSegments): Dereference {\n  let body = \"if (typeof(it) !== 'undefined'\";\n  if (path.length === 0) {\n    return (it): unknown => it;\n  }\n  body = path.reduce((body, _, i) => {\n    return (\n      body +\n      \"\\n\\t&& it !== null && typeof((it = it['\" +\n      replace(replace(path[i] + '', '\\\\', '\\\\\\\\'), \"'\", \"\\\\'\") +\n      \"'])) !== 'undefined'\"\n    );\n  }, \"if (typeof(it) !== 'undefined'\") as string;\n  body = body + ') {\\n\\treturn it;\\n }';\n  // eslint-disable-next-line no-new-func\n  return new Function('it', body) as Dereference;\n}\n\nexport function setValueAtPath(\n  target: unknown,\n  val: unknown,\n  path: PathSegments,\n  force = false,\n): unknown {\n  if (path.length === 0) {\n    throw new Error('Cannot set the root object; assign it directly.');\n  }\n  if (typeof target === 'undefined') {\n    throw new TypeError('Cannot set values on undefined');\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let it: any = target;\n  const len = path.length;\n  const end = path.length - 1;\n  let step: PathSegment;\n  let cursor = -1;\n  let rem: unknown;\n  let p: number;\n  while (++cursor < len) {\n    step = path[cursor];\n    if (typeof step !== 'string' && typeof step !== 'number') {\n      throw new TypeError('PathSegments must be a string or a number.');\n    }\n    if (\n      // Reconsider this strategy. It disallows legitimate structures on\n      // non - objects, or more precisely, on objects not derived from a class\n      // or constructor function.\n      step === '__proto__' ||\n      step === 'constructor' ||\n      step === 'prototype'\n    ) {\n      throw new Error('Attempted prototype pollution disallowed.');\n    }\n    if (Array.isArray(it)) {\n      if (step === '-' && cursor === end) {\n        it.push(val);\n        return undefined;\n      }\n      p = toArrayIndexReference(it, step);\n      if (it.length > p) {\n        if (cursor === end) {\n          rem = it[p];\n          it[p] = val;\n          break;\n        }\n        it = it[p];\n      } else if (cursor === end && p === it.length) {\n        if (force) {\n          it.push(val);\n          return undefined;\n        }\n      } else if (force) {\n        it = it[p] = cursor === end ? val : {};\n      }\n    } else {\n      if (typeof it[step] === 'undefined') {\n        if (force) {\n          if (cursor === end) {\n            it[step] = val;\n            return undefined;\n          }\n          // if the next step is an array index, this step should be an array.\n          const n = Number(path[cursor + 1]);\n          if (\n            Number.isInteger(n) &&\n            toArrayIndexReference(it[step], n) !== -1\n          ) {\n            it = it[step] = [];\n            continue;\n          }\n          it = it[step] = {};\n          continue;\n        }\n        return undefined;\n      }\n      if (cursor === end) {\n        rem = it[step];\n        it[step] = val;\n        break;\n      }\n      it = it[step];\n    }\n  }\n  return rem;\n}\n\nexport function unsetValueAtPath(target: unknown, path: PathSegments): unknown {\n  if (path.length === 0) {\n    throw new Error('Cannot unset the root object; assign it directly.');\n  }\n  if (typeof target === 'undefined') {\n    throw new TypeError('Cannot unset values on undefined');\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let it: any = target;\n  const len = path.length;\n  const end = path.length - 1;\n  let step: PathSegment;\n  let cursor = -1;\n  let rem: unknown;\n  let p: number;\n  while (++cursor < len) {\n    step = path[cursor];\n    if (typeof step !== 'string' && typeof step !== 'number') {\n      throw new TypeError('PathSegments must be a string or a number.');\n    }\n    if (\n      step === '__proto__' ||\n      step === 'constructor' ||\n      step === 'prototype'\n    ) {\n      throw new Error('Attempted prototype pollution disallowed.');\n    }\n    if (Array.isArray(it)) {\n      p = toArrayIndexReference(it, step);\n      if (p >= it.length) return undefined;\n      if (cursor === end) {\n        rem = it[p];\n        delete it[p];\n        break;\n      }\n      it = it[p];\n    } else {\n      if (typeof it[step] === 'undefined') {\n        return undefined;\n      }\n      if (cursor === end) {\n        rem = it[step];\n        delete it[step];\n        break;\n      }\n      it = it[step];\n    }\n  }\n  return rem;\n}\n\nexport function looksLikeFragment(ptr: Pointer): boolean {\n  return typeof ptr === 'string' && ptr.length > 0 && ptr[0] === '#';\n}\n\nexport function pickDecoder(ptr: Pointer): Decoder {\n  return looksLikeFragment(ptr) ? decodeUriFragmentIdentifier : decodePointer;\n}\n\nexport function decodePtrInit(ptr: Pointer | PathSegments): PathSegments {\n  return Array.isArray(ptr)\n    ? ptr.slice(0)\n    : pickDecoder(ptr as Pointer)(ptr as Pointer);\n}\n", "import {\n  Dereference,\n  decodePtrInit,\n  compilePointerDereference,\n  setValueAtPath,\n  encodePointer,\n  encodeUriFragmentIdentifier,\n  pickDecoder,\n  unsetValueAtPath,\n  decodeRelativePointer,\n} from './util';\nimport {\n  JsonStringPointer,\n  UriFragmentIdentifierPointer,\n  Pointer,\n  RelativeJsonPointer,\n  PathSegments,\n  Encoder,\n  JsonStringPointerListItem,\n  UriFragmentIdentifierPointerListItem,\n} from './types';\n\n/**\n * Determines if the value is an object (not null)\n * @param value the value\n * @returns true if the value is a non-null object; otherwise false.\n *\n * @hidden\n */\nfunction isObject(value: unknown): boolean {\n  return typeof value === 'object' && value !== null;\n}\n\n/**\n * Signature of visitor functions, used with [[JsonPointer.visit]] method. Visitors are callbacks invoked for every segment/branch of a target's object graph.\n *\n * Tree descent occurs in-order, breadth first.\n */\nexport type Visitor = (ptr: <PERSON><PERSON><PERSON>tringPointer, val: unknown) => void;\n\n/** @hidden */\ninterface Item {\n  obj: unknown;\n  path: PathSegments;\n}\n\n/** @hidden */\nfunction shouldDescend(obj: unknown): boolean {\n  return isObject(obj) && !JsonReference.isReference(obj);\n}\n/** @hidden */\nfunction descendingVisit(\n  target: unknown,\n  visitor: Visitor,\n  encoder: Encoder,\n): void {\n  const distinctObjects = new Map<unknown, JsonPointer>();\n  const q: Item[] = [{ obj: target, path: [] }];\n  while (q.length) {\n    const { obj, path } = q.shift() as Item;\n    visitor(encoder(path), obj);\n    if (shouldDescend(obj)) {\n      distinctObjects.set(\n        obj,\n        new JsonPointer(encodeUriFragmentIdentifier(path)),\n      );\n      if (!Array.isArray(obj)) {\n        const keys = Object.keys(obj as Record<string, unknown>);\n        const len = keys.length;\n        let i = -1;\n        while (++i < len) {\n          const it = (obj as Record<string, unknown>)[keys[i]];\n          if (isObject(it) && distinctObjects.has(it)) {\n            q.push({\n              obj: new JsonReference(distinctObjects.get(it) as JsonPointer),\n              path: path.concat(keys[i]),\n            });\n          } else {\n            q.push({\n              obj: it,\n              path: path.concat(keys[i]),\n            });\n          }\n        }\n      } else {\n        // handleArray\n        let j = -1;\n        const len = obj.length;\n        while (++j < len) {\n          const it = obj[j];\n          if (isObject(it) && distinctObjects.has(it)) {\n            q.push({\n              obj: new JsonReference(distinctObjects.get(it) as JsonPointer),\n              path: path.concat([j + '']),\n            });\n          } else {\n            q.push({\n              obj: it,\n              path: path.concat([j + '']),\n            });\n          }\n        }\n      }\n    }\n  }\n}\n\n/** @hidden */\nconst $ptr = Symbol('pointer');\n/** @hidden */\nconst $frg = Symbol('fragmentId');\n/** @hidden */\nconst $get = Symbol('getter');\n\n/**\n * Represents a JSON Pointer, capable of getting and setting the value on target\n * objects at the pointer's location.\n *\n * While there are static variants for most operations, our recommendation is\n * to use the instance level methods, which enables you avoid repeated\n * compiling/emitting transient accessors. Take a look at the speed comparisons\n * for our justification.\n *\n * In most cases, you should create and reuse instances of JsonPointer within\n * scope that makes sense for your app. We often create constants for frequently\n * used pointers, but your use case may vary.\n *\n * The following is a contrived example showing a function that uses pointers to\n * deal with changes in the structure of data (a version independent function):\n *\n * ```ts\n * import { JsonPointer } from 'json-ptr';\n *\n * export type SupportedVersion = '1.0' | '1.1';\n *\n * interface PrimaryGuestNamePointers {\n *   name: JsonPointer;\n *   surname: JsonPointer;\n *   honorific: JsonPointer;\n * }\n * const versions: Record<SupportedVersion, PrimaryGuestNamePointers> = {\n *   '1.0': {\n *     name: JsonPointer.create('/guests/0/name'),\n *     surname: JsonPointer.create('/guests/0/surname'),\n *     honorific: JsonPointer.create('/guests/0/honorific'),\n *   },\n *   '1.1': {\n *     name: JsonPointer.create('/primary/primaryGuest/name'),\n *     surname: JsonPointer.create('/primary/primaryGuest/surname'),\n *     honorific: JsonPointer.create('/primary/primaryGuest/honorific'),\n *   }\n * };\n *\n * interface Reservation extends Record<string, unknown> {\n *   version?: SupportedVersion;\n * }\n *\n * function primaryGuestName(reservation: Reservation): string {\n *   const pointers = versions[reservation.version || '1.0'];\n *   const name = pointers.name.get(reservation) as string;\n *   const surname = pointers.surname.get(reservation) as string;\n *   const honorific = pointers.honorific.get(reservation) as string;\n *   const names: string[] = [];\n *   if (honorific) names.push(honorific);\n *   if (name) names.push(name);\n *   if (surname) names.push(surname);\n *   return names.join(' ');\n * }\n *\n * // The original layout of a reservation (only the parts relevant to our example)\n * const reservationV1: Reservation = {\n *   guests: [{\n *     name: 'Wilbur',\n *     surname: 'Finkle',\n *     honorific: 'Mr.'\n *   }, {\n *     name: 'Wanda',\n *     surname: 'Finkle',\n *     honorific: 'Mrs.'\n *   }, {\n *     name: 'Wilma',\n *     surname: 'Finkle',\n *     honorific: 'Miss',\n *     child: true,\n *     age: 12\n *   }]\n *   // ...\n * };\n *\n * // The new layout of a reservation (only the parts relevant to our example)\n * const reservationV1_1: Reservation = {\n *   version: '1.1',\n *   primary: {\n *     primaryGuest: {\n *       name: 'Wilbur',\n *       surname: 'Finkle',\n *       honorific: 'Mr.'\n *     },\n *     additionalGuests: [{\n *       name: 'Wanda',\n *       surname: 'Finkle',\n *       honorific: 'Mrs.'\n *     }, {\n *       name: 'Wilma',\n *       surname: 'Finkle',\n *       honorific: 'Miss',\n *       child: true,\n *       age: 12\n *     }]\n *     // ...\n *   }\n *   // ...\n * };\n *\n * console.log(primaryGuestName(reservationV1));\n * console.log(primaryGuestName(reservationV1_1));\n *\n * ```\n *\n * There are many uses for pointers.\n */\nexport class JsonPointer {\n  /** @hidden */\n  private [$ptr]: JsonStringPointer;\n  /** @hidden */\n  private [$frg]: UriFragmentIdentifierPointer;\n  /** @hidden */\n  private [$get]: Dereference;\n\n  /**\n   * Factory function that creates a JsonPointer instance.\n   *\n   * ```ts\n   * const ptr = JsonPointer.create('/deeply/nested/data/0/here');\n   * ```\n   * _or_\n   * ```ts\n   * const ptr = JsonPointer.create(['deeply', 'nested', 'data', 0, 'here']);\n   * ```\n   * @param pointer the pointer or path.\n   */\n  static create(pointer: Pointer | PathSegments): JsonPointer {\n    return new JsonPointer(pointer);\n  }\n\n  /**\n   * Determines if the specified `target`'s object graph has a value at the `pointer`'s location.\n   *\n   * ```ts\n   * const target = {\n   *   first: 'second',\n   *   third: ['fourth', 'fifth', { sixth: 'seventh' }],\n   *   eighth: 'ninth'\n   * };\n   *\n   * console.log(JsonPointer.has(target, '/third/0'));\n   * // true\n   * console.log(JsonPointer.has(target, '/tenth'));\n   * // false\n   * ```\n   *\n   * @param target the target of the operation\n   * @param pointer the pointer or path\n   */\n  static has(\n    target: unknown,\n    pointer: Pointer | PathSegments | JsonPointer,\n  ): boolean {\n    if (typeof pointer === 'string' || Array.isArray(pointer)) {\n      pointer = new JsonPointer(pointer);\n    }\n    return (pointer as JsonPointer).has(target);\n  }\n\n  /**\n   * Gets the `target` object's value at the `pointer`'s location.\n   *\n   * ```ts\n   * const target = {\n   *   first: 'second',\n   *   third: ['fourth', 'fifth', { sixth: 'seventh' }],\n   *   eighth: 'ninth'\n   * };\n   *\n   * console.log(JsonPointer.get(target, '/third/2/sixth'));\n   * // seventh\n   * console.log(JsonPointer.get(target, '/tenth'));\n   * // undefined\n   * ```\n   *\n   * @param target the target of the operation\n   * @param pointer the pointer or path.\n   */\n  static get(\n    target: unknown,\n    pointer: Pointer | PathSegments | JsonPointer,\n  ): unknown {\n    if (typeof pointer === 'string' || Array.isArray(pointer)) {\n      pointer = new JsonPointer(pointer);\n    }\n    return (pointer as JsonPointer).get(target);\n  }\n\n  /**\n   * Sets the `target` object's value, as specified, at the `pointer`'s location.\n   *\n   * ```ts\n   * const target = {\n   *   first: 'second',\n   *   third: ['fourth', 'fifth', { sixth: 'seventh' }],\n   *   eighth: 'ninth'\n   * };\n   *\n   * console.log(JsonPointer.set(target, '/third/2/sixth', 'tenth'));\n   * // seventh\n   * console.log(JsonPointer.set(target, '/tenth', 'eleventh', true));\n   * // undefined\n   * console.log(JSON.stringify(target, null, ' '));\n   * // {\n   * // \"first\": \"second\",\n   * // \"third\": [\n   * //  \"fourth\",\n   * //  \"fifth\",\n   * //  {\n   * //   \"sixth\": \"tenth\"\n   * //  }\n   * // ],\n   * // \"eighth\": \"ninth\",\n   * // \"tenth\": \"eleventh\"\n   * // }\n   * ```\n   *\n   * @param target the target of the operation\n   * @param pointer the pointer or path\n   * @param val a value to write into the object graph at the specified pointer location\n   * @param force indications whether the operation should force the pointer's location into existence in the object graph.\n   *\n   * @returns the prior value at the pointer's location in the object graph.\n   */\n  static set(\n    target: unknown,\n    pointer: Pointer | PathSegments | JsonPointer,\n    val: unknown,\n    force = false,\n  ): unknown {\n    if (typeof pointer === 'string' || Array.isArray(pointer)) {\n      pointer = new JsonPointer(pointer);\n    }\n    return (pointer as JsonPointer).set(target, val, force);\n  }\n\n  /**\n   * Removes the `target` object's value at the `pointer`'s location.\n   *\n   * ```ts\n   * const target = {\n   *   first: 'second',\n   *   third: ['fourth', 'fifth', { sixth: 'seventh' }],\n   *   eighth: 'ninth'\n   * };\n   *\n   * console.log(JsonPointer.unset(target, '/third/2/sixth'));\n   * // seventh\n   * console.log(JsonPointer.unset(target, '/tenth'));\n   * // undefined\n   * console.log(JSON.stringify(target, null, ' '));\n   * // {\n   * // \"first\": \"second\",\n   * // \"third\": [\n   * //  \"fourth\",\n   * //  \"fifth\",\n   * //  {}\n   * // ],\n   * // \"eighth\": \"ninth\",\n   * // }\n   * ```\n   * @param target the target of the operation\n   * @param pointer the pointer or path\n   *\n   * @returns the value that was removed from the object graph.\n   */\n  static unset(\n    target: unknown,\n    pointer: Pointer | PathSegments | JsonPointer,\n  ): unknown {\n    if (typeof pointer === 'string' || Array.isArray(pointer)) {\n      pointer = new JsonPointer(pointer);\n    }\n    return (pointer as JsonPointer).unset(target);\n  }\n\n  /**\n   * Decodes the specified pointer into path segments.\n   * @param pointer a string representation of a JSON Pointer\n   */\n  static decode(pointer: Pointer): PathSegments {\n    return pickDecoder(pointer)(pointer);\n  }\n\n  /**\n   * Evaluates the target's object graph, calling the specified visitor for every unique pointer location discovered while walking the graph.\n   * @param target the target of the operation\n   * @param visitor a callback function invoked for each unique pointer location in the object graph\n   * @param fragmentId indicates whether the visitor should receive fragment identifiers or regular pointers\n   */\n  static visit(target: unknown, visitor: Visitor, fragmentId = false): void {\n    descendingVisit(\n      target,\n      visitor,\n      fragmentId ? encodeUriFragmentIdentifier : encodePointer,\n    );\n  }\n\n  /**\n   * Evaluates the target's object graph, returning a [[JsonStringPointerListItem]] for each location in the graph.\n   * @param target the target of the operation\n   */\n  static listPointers(target: unknown): JsonStringPointerListItem[] {\n    const res: JsonStringPointerListItem[] = [];\n    descendingVisit(\n      target,\n      (pointer, value): void => {\n        res.push({ pointer, value });\n      },\n      encodePointer,\n    );\n    return res;\n  }\n\n  /**\n   * Evaluates the target's object graph, returning a [[UriFragmentIdentifierPointerListItem]] for each location in the graph.\n   * @param target the target of the operation\n   */\n  static listFragmentIds(\n    target: unknown,\n  ): UriFragmentIdentifierPointerListItem[] {\n    const res: UriFragmentIdentifierPointerListItem[] = [];\n    descendingVisit(\n      target,\n      (fragmentId, value): void => {\n        res.push({ fragmentId, value });\n      },\n      encodeUriFragmentIdentifier,\n    );\n    return res;\n  }\n\n  /**\n   * Evaluates the target's object graph, returning a Record&lt;Pointer, unknown> populated with pointers and the corresponding values from the graph.\n   * @param target the target of the operation\n   * @param fragmentId indicates whether the results are populated with fragment identifiers rather than regular pointers\n   */\n  static flatten(\n    target: unknown,\n    fragmentId = false,\n  ): Record<Pointer, unknown> {\n    const res: Record<Pointer, unknown> = {};\n    descendingVisit(\n      target,\n      (p, v) => {\n        res[p] = v;\n      },\n      fragmentId ? encodeUriFragmentIdentifier : encodePointer,\n    );\n    return res;\n  }\n\n  /**\n   * Evaluates the target's object graph, returning a Map&lt;Pointer,unknown>  populated with pointers and the corresponding values form the graph.\n   * @param target the target of the operation\n   * @param fragmentId indicates whether the results are populated with fragment identifiers rather than regular pointers\n   */\n  static map(target: unknown, fragmentId = false): Map<Pointer, unknown> {\n    const res = new Map<Pointer, unknown>();\n    descendingVisit(\n      target,\n      res.set.bind(res),\n      fragmentId ? encodeUriFragmentIdentifier : encodePointer,\n    );\n    return res;\n  }\n\n  /**\n   * The pointer's decoded path segments.\n   */\n  public readonly path: PathSegments;\n\n  /**\n   * Creates a new instance.\n   * @param ptr a string representation of a JSON Pointer, or a decoded array of path segments.\n   */\n  constructor(ptr: Pointer | PathSegments) {\n    this.path = decodePtrInit(ptr);\n  }\n\n  /**\n   * Gets the target object's value at the pointer's location.\n   * @param target the target of the operation\n   */\n  get(target: unknown): unknown {\n    if (!this[$get]) {\n      this[$get] = compilePointerDereference(this.path);\n    }\n    return this[$get](target);\n  }\n\n  /**\n   * Sets the target object's value, as specified, at the pointer's location.\n   *\n   * If any part of the pointer's path does not exist, the operation aborts\n   * without modification, unless the caller indicates that pointer's location\n   * should be created.\n   *\n   * @param target the target of the operation\n   * @param value the value to set\n   * @param force indicates whether the pointer's location should be created if it doesn't already exist.\n   */\n  set(target: unknown, value: unknown, force = false): unknown {\n    return setValueAtPath(target, value, this.path, force);\n  }\n\n  /**\n   * Removes the target object's value at the pointer's location.\n   * @param target the target of the operation\n   *\n   * @returns the value that was removed from the object graph.\n   */\n  unset(target: unknown): unknown {\n    return unsetValueAtPath(target, this.path);\n  }\n\n  /**\n   * Determines if the specified target's object graph has a value at the pointer's location.\n   * @param target the target of the operation\n   */\n  has(target: unknown): boolean {\n    return typeof this.get(target) !== 'undefined';\n  }\n\n  /**\n   * Gets the value in the object graph that is the parent of the pointer location.\n   * @param target the target of the operation\n   */\n  parent(target: unknown): unknown {\n    const p = this.path;\n    if (p.length == 1) return undefined;\n    const parent = new JsonPointer(p.slice(0, p.length - 1));\n    return parent.get(target);\n  }\n\n  /**\n   * Creates a new JsonPointer instance, pointing to the specified relative location in the object graph.\n   * @param ptr the relative pointer (relative to this)\n   * @returns A new instance that points to the relative location.\n   */\n  relative(ptr: RelativeJsonPointer): JsonPointer {\n    const p = this.path;\n    const decoded = decodeRelativePointer(ptr) as string[];\n    const n = parseInt(decoded[0]);\n    if (n > p.length) throw new Error('Relative location does not exist.');\n    const r = p.slice(0, p.length - n).concat(decoded.slice(1));\n    if (decoded[0][decoded[0].length - 1] == '#') {\n      // It references the path segment/name, not the value\n      const name = r[r.length - 1] as string;\n      throw new Error(\n        `We won't compile a pointer that will always return '${name}'. Use JsonPointer.rel(target, ptr) instead.`,\n      );\n    }\n    return new JsonPointer(r);\n  }\n\n  /**\n   * Resolves the specified relative pointer path against the specified target object, and gets the target object's value at the relative pointer's location.\n   * @param target the target of the operation\n   * @param ptr the relative pointer (relative to this)\n   * @returns the value at the relative pointer's resolved path; otherwise undefined.\n   */\n  rel(target: unknown, ptr: RelativeJsonPointer): unknown {\n    const p = this.path;\n    const decoded = decodeRelativePointer(ptr) as string[];\n    const n = parseInt(decoded[0]);\n    if (n > p.length) {\n      // out of bounds\n      return undefined;\n    }\n    const r = p.slice(0, p.length - n).concat(decoded.slice(1));\n    const other = new JsonPointer(r);\n    if (decoded[0][decoded[0].length - 1] == '#') {\n      // It references the path segment/name, not the value\n      const name = r[r.length - 1] as string;\n      const parent = other.parent(target);\n      return Array.isArray(parent) ? parseInt(name, 10) : name;\n    }\n    return other.get(target);\n  }\n\n  /**\n   * Creates a new instance by concatenating the specified pointer's path onto this pointer's path.\n   * @param ptr the string representation of a pointer, it's decoded path, or an instance of JsonPointer indicating the additional path to concatenate onto the pointer.\n   */\n  concat(ptr: JsonPointer | Pointer | PathSegments): JsonPointer {\n    return new JsonPointer(\n      this.path.concat(\n        ptr instanceof JsonPointer ? ptr.path : decodePtrInit(ptr),\n      ),\n    );\n  }\n\n  /**\n   * This pointer's JSON Pointer encoded string representation.\n   */\n  get pointer(): JsonStringPointer {\n    if (this[$ptr] === undefined) {\n      this[$ptr] = encodePointer(this.path);\n    }\n    return this[$ptr];\n  }\n\n  /**\n   * This pointer's URI fragment identifier encoded string representation.\n   */\n  get uriFragmentIdentifier(): UriFragmentIdentifierPointer {\n    if (!this[$frg]) {\n      this[$frg] = encodeUriFragmentIdentifier(this.path);\n    }\n    return this[$frg];\n  }\n\n  /**\n   * Emits the JSON Pointer encoded string representation.\n   */\n  toString(): string {\n    return this.pointer;\n  }\n}\n\n/** @hidden */\nconst $pointer = Symbol('pointer');\n\n/**\n * A reference to a location in an object graph.\n *\n * This type is used by this module to break cycles in an object graph and to\n * reference locations that have already been visited when enumerating pointers.\n */\nexport class JsonReference {\n  /**\n   * Determines if the specified `candidate` is a JsonReference.\n   * @param candidate the candidate\n   */\n  static isReference(candidate: unknown): candidate is JsonReference {\n    if (!candidate) return false;\n    const ref = candidate as unknown as JsonReference;\n    return typeof ref.$ref === 'string' && typeof ref.resolve === 'function';\n  }\n\n  /** @hidden */\n  private readonly [$pointer]: JsonPointer;\n\n  /**\n   * A reference to a position if an object graph.\n   */\n  public readonly $ref: UriFragmentIdentifierPointer;\n\n  /**\n   * Creates a new instance.\n   * @param pointer a JSON Pointer for the reference.\n   */\n  constructor(pointer: JsonPointer | Pointer | PathSegments) {\n    this[$pointer] =\n      pointer instanceof JsonPointer ? pointer : new JsonPointer(pointer);\n    this.$ref = this[$pointer].uriFragmentIdentifier;\n  }\n\n  /**\n   * Resolves the reference against the `target` object, returning the value at\n   * the referenced pointer's location.\n   * @param target the target object\n   */\n  resolve(target: unknown): unknown {\n    return this[$pointer].get(target);\n  }\n\n  /**\n   * Gets the reference's pointer.\n   */\n  pointer(): JsonPointer {\n    return this[$pointer];\n  }\n\n  /**\n   * Gets the reference pointer's string representation (a URI fragment identifier).\n   */\n  toString(): string {\n    return this.$ref;\n  }\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "replace", "source", "find", "repl", "res", "rem", "beg", "end", "indexOf", "substring", "length", "decodeFragmentSegments", "segments", "i", "len", "Array", "decodeURIComponent", "encodeFragmentSegments", "encodeURIComponent", "decodePointerSegments", "encodePointerSegments", "decodePointer", "ptr", "TypeError", "ReferenceError", "split", "encodePointer", "path", "isArray", "concat", "join", "decodeUriFragmentIdentifier", "encodeUriFragmentIdentifier", "InvalidRelativePointerError", "decodeRelativePointer", "first", "substr", "slice", "unshift", "toArrayIndexReference", "arr", "idx", "cursor", "parseInt", "compilePointerDereference", "body", "it", "reduce", "_", "Function", "setValueAtPath", "target", "val", "force", "Error", "step", "p", "push", "n", "Number", "isInteger", "unsetValueAtPath", "looksLikeFragment", "pickDecoder", "decodePtrInit", "isObject", "shouldDescend", "JsonReference", "isReference", "descendingVisit", "visitor", "encoder", "distinctObjects", "Map", "q", "shift", "set", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j", "has", "keys", "$ptr", "$frg", "$get", "this", "create", "pointer", "unset", "decode", "visit", "fragmentId", "listPointers", "listFragmentIds", "flatten", "v", "map", "bind", "parent", "relative", "decoded", "r", "rel", "other", "undefined", "toString", "$pointer", "$ref", "uriFragmentIdentifier", "candidate", "ref", "resolve"], "sourceRoot": ""}