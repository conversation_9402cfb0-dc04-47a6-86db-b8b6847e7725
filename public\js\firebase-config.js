// Firebase Configuration
// ملاحظة: يجب استبدال هذه القيم بقيم مشروعك الفعلي من Firebase Console
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};

// Initialize Firebase
import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getFunctions } from 'firebase/functions';

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const functions = getFunctions(app);

// Google Auth Provider
export const googleProvider = new GoogleAuthProvider();

// Gemini API Configuration
export const GEMINI_API_KEY = 'AIzaSyCYsL7v_v0OmtxEckHYQ1j-g2J1eQKv6H8';
export const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

// App Configuration
export const APP_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedFileTypes: ['application/pdf'],
  supportedLanguages: ['ar', 'en'],
  questionTypes: ['mcq', 'true_false', 'fill_blank'],
  maxQuestionsPerExam: 50
};

console.log('Firebase initialized successfully');
