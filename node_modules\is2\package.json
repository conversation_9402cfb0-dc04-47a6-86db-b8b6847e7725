{"name": "is2", "version": "2.0.9", "description": "A type checking library where each exported function returns either true or false and does not throw. Also added tests.", "license": "MIT", "tags": ["type", "check", "checker", "checking", "utilities", "network", "networking", "credit", "card", "validation"], "keywords": ["type", "check", "checker", "checking", "utilities", "network", "networking", "credit", "card", "validation"], "author": "<PERSON> <<EMAIL>>", "maintainers": "<PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>", "homepage": "http://github.com/stdarg/is2", "repository": {"type": "git", "url": "**************:stdarg/is2.git"}, "bugs": {"url": "http://github.com/stdarg/is/issues"}, "main": "./index.js", "scripts": {"test": "./node_modules/.bin/mocha -C --reporter list tests.js"}, "engines": {"node": ">=v0.10.0"}, "dependencies": {"deep-is": "^0.1.3", "ip-regex": "^4.1.0", "is-url": "^1.2.4"}, "devDependencies": {"mocha": "6.2.3", "mongodb": "3.2.4"}}