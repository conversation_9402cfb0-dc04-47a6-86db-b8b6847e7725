(e=>{"function"==typeof define&&define.amd?define(["protobufjs/minimal"],e):"function"==typeof require&&"object"==typeof module&&module&&module.exports&&(module.exports=e(require("protobufjs/minimal")))})(function(e){var t,n,r,i=e.Reader,o=e.Writer,l=e.util,s=e.roots.default||(e.roots.default={});function a(e){if(this.rules=[],e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}function p(e){if(this.additional_bindings=[],e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}function u(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return s.google=((r={}).api=((n={}).Http=(a.prototype.rules=l.emptyArray,a.prototype.fully_decode_reserved_expansion=!1,a.create=function(e){return new a(e)},a.encode=function(e,t){if(t=t||o.create(),null!=e.rules&&e.rules.length)for(var n=0;n<e.rules.length;++n)s.google.api.HttpRule.encode(e.rules[n],t.uint32(10).fork()).ldelim();return null!=e.fully_decode_reserved_expansion&&Object.hasOwnProperty.call(e,"fully_decode_reserved_expansion")&&t.uint32(16).bool(e.fully_decode_reserved_expansion),t},a.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},a.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new s.google.api.Http;e.pos<n;){var o=e.uint32();switch(o>>>3){case 1:r.rules&&r.rules.length||(r.rules=[]),r.rules.push(s.google.api.HttpRule.decode(e,e.uint32()));break;case 2:r.fully_decode_reserved_expansion=e.bool();break;default:e.skipType(7&o)}}return r},a.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},a.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.rules&&e.hasOwnProperty("rules")){if(!Array.isArray(e.rules))return"rules: array expected";for(var t=0;t<e.rules.length;++t){var n=s.google.api.HttpRule.verify(e.rules[t]);if(n)return"rules."+n}}return null!=e.fully_decode_reserved_expansion&&e.hasOwnProperty("fully_decode_reserved_expansion")&&"boolean"!=typeof e.fully_decode_reserved_expansion?"fully_decode_reserved_expansion: boolean expected":null},a.fromObject=function(e){if(e instanceof s.google.api.Http)return e;var t=new s.google.api.Http;if(e.rules){if(!Array.isArray(e.rules))throw TypeError(".google.api.Http.rules: array expected");t.rules=[];for(var n=0;n<e.rules.length;++n){if("object"!=typeof e.rules[n])throw TypeError(".google.api.Http.rules: object expected");t.rules[n]=s.google.api.HttpRule.fromObject(e.rules[n])}}return null!=e.fully_decode_reserved_expansion&&(t.fully_decode_reserved_expansion=Boolean(e.fully_decode_reserved_expansion)),t},a.toObject=function(e,t){var n={};if(((t=t||{}).arrays||t.defaults)&&(n.rules=[]),t.defaults&&(n.fully_decode_reserved_expansion=!1),e.rules&&e.rules.length){n.rules=[];for(var r=0;r<e.rules.length;++r)n.rules[r]=s.google.api.HttpRule.toObject(e.rules[r],t)}return null!=e.fully_decode_reserved_expansion&&e.hasOwnProperty("fully_decode_reserved_expansion")&&(n.fully_decode_reserved_expansion=e.fully_decode_reserved_expansion),n},a.prototype.toJSON=function(){return this.constructor.toObject(this,e.util.toJSONOptions)},a),n.HttpRule=(p.prototype.selector="",p.prototype.get="",p.prototype.put="",p.prototype.post="",p.prototype.delete="",p.prototype.patch="",p.prototype.custom=null,p.prototype.body="",p.prototype.response_body="",p.prototype.additional_bindings=l.emptyArray,Object.defineProperty(p.prototype,"pattern",{get:l.oneOfGetter(t=["get","put","post","delete","patch","custom"]),set:l.oneOfSetter(t)}),p.create=function(e){return new p(e)},p.encode=function(e,t){if(t=t||o.create(),null!=e.selector&&Object.hasOwnProperty.call(e,"selector")&&t.uint32(10).string(e.selector),null!=e.get&&Object.hasOwnProperty.call(e,"get")&&t.uint32(18).string(e.get),null!=e.put&&Object.hasOwnProperty.call(e,"put")&&t.uint32(26).string(e.put),null!=e.post&&Object.hasOwnProperty.call(e,"post")&&t.uint32(34).string(e.post),null!=e.delete&&Object.hasOwnProperty.call(e,"delete")&&t.uint32(42).string(e.delete),null!=e.patch&&Object.hasOwnProperty.call(e,"patch")&&t.uint32(50).string(e.patch),null!=e.body&&Object.hasOwnProperty.call(e,"body")&&t.uint32(58).string(e.body),null!=e.custom&&Object.hasOwnProperty.call(e,"custom")&&s.google.api.CustomHttpPattern.encode(e.custom,t.uint32(66).fork()).ldelim(),null!=e.additional_bindings&&e.additional_bindings.length)for(var n=0;n<e.additional_bindings.length;++n)s.google.api.HttpRule.encode(e.additional_bindings[n],t.uint32(90).fork()).ldelim();return null!=e.response_body&&Object.hasOwnProperty.call(e,"response_body")&&t.uint32(98).string(e.response_body),t},p.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},p.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new s.google.api.HttpRule;e.pos<n;){var o=e.uint32();switch(o>>>3){case 1:r.selector=e.string();break;case 2:r.get=e.string();break;case 3:r.put=e.string();break;case 4:r.post=e.string();break;case 5:r.delete=e.string();break;case 6:r.patch=e.string();break;case 8:r.custom=s.google.api.CustomHttpPattern.decode(e,e.uint32());break;case 7:r.body=e.string();break;case 12:r.response_body=e.string();break;case 11:r.additional_bindings&&r.additional_bindings.length||(r.additional_bindings=[]),r.additional_bindings.push(s.google.api.HttpRule.decode(e,e.uint32()));break;default:e.skipType(7&o)}}return r},p.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},p.verify=function(e){if("object"!=typeof e||null===e)return"object expected";var t={};if(null!=e.selector&&e.hasOwnProperty("selector")&&!l.isString(e.selector))return"selector: string expected";if(null!=e.get&&e.hasOwnProperty("get")&&(t.pattern=1,!l.isString(e.get)))return"get: string expected";if(null!=e.put&&e.hasOwnProperty("put")){if(1===t.pattern)return"pattern: multiple values";if(t.pattern=1,!l.isString(e.put))return"put: string expected"}if(null!=e.post&&e.hasOwnProperty("post")){if(1===t.pattern)return"pattern: multiple values";if(t.pattern=1,!l.isString(e.post))return"post: string expected"}if(null!=e.delete&&e.hasOwnProperty("delete")){if(1===t.pattern)return"pattern: multiple values";if(t.pattern=1,!l.isString(e.delete))return"delete: string expected"}if(null!=e.patch&&e.hasOwnProperty("patch")){if(1===t.pattern)return"pattern: multiple values";if(t.pattern=1,!l.isString(e.patch))return"patch: string expected"}if(null!=e.custom&&e.hasOwnProperty("custom")){if(1===t.pattern)return"pattern: multiple values";if(t.pattern=1,n=s.google.api.CustomHttpPattern.verify(e.custom))return"custom."+n}if(null!=e.body&&e.hasOwnProperty("body")&&!l.isString(e.body))return"body: string expected";if(null!=e.response_body&&e.hasOwnProperty("response_body")&&!l.isString(e.response_body))return"response_body: string expected";if(null!=e.additional_bindings&&e.hasOwnProperty("additional_bindings")){if(!Array.isArray(e.additional_bindings))return"additional_bindings: array expected";for(var n,r=0;r<e.additional_bindings.length;++r)if(n=s.google.api.HttpRule.verify(e.additional_bindings[r]))return"additional_bindings."+n}return null},p.fromObject=function(e){if(e instanceof s.google.api.HttpRule)return e;var t=new s.google.api.HttpRule;if(null!=e.selector&&(t.selector=String(e.selector)),null!=e.get&&(t.get=String(e.get)),null!=e.put&&(t.put=String(e.put)),null!=e.post&&(t.post=String(e.post)),null!=e.delete&&(t.delete=String(e.delete)),null!=e.patch&&(t.patch=String(e.patch)),null!=e.custom){if("object"!=typeof e.custom)throw TypeError(".google.api.HttpRule.custom: object expected");t.custom=s.google.api.CustomHttpPattern.fromObject(e.custom)}if(null!=e.body&&(t.body=String(e.body)),null!=e.response_body&&(t.response_body=String(e.response_body)),e.additional_bindings){if(!Array.isArray(e.additional_bindings))throw TypeError(".google.api.HttpRule.additional_bindings: array expected");t.additional_bindings=[];for(var n=0;n<e.additional_bindings.length;++n){if("object"!=typeof e.additional_bindings[n])throw TypeError(".google.api.HttpRule.additional_bindings: object expected");t.additional_bindings[n]=s.google.api.HttpRule.fromObject(e.additional_bindings[n])}}return t},p.toObject=function(e,t){var n={};if(((t=t||{}).arrays||t.defaults)&&(n.additional_bindings=[]),t.defaults&&(n.selector="",n.body="",n.response_body=""),null!=e.selector&&e.hasOwnProperty("selector")&&(n.selector=e.selector),null!=e.get&&e.hasOwnProperty("get")&&(n.get=e.get,t.oneofs)&&(n.pattern="get"),null!=e.put&&e.hasOwnProperty("put")&&(n.put=e.put,t.oneofs)&&(n.pattern="put"),null!=e.post&&e.hasOwnProperty("post")&&(n.post=e.post,t.oneofs)&&(n.pattern="post"),null!=e.delete&&e.hasOwnProperty("delete")&&(n.delete=e.delete,t.oneofs)&&(n.pattern="delete"),null!=e.patch&&e.hasOwnProperty("patch")&&(n.patch=e.patch,t.oneofs)&&(n.pattern="patch"),null!=e.body&&e.hasOwnProperty("body")&&(n.body=e.body),null!=e.custom&&e.hasOwnProperty("custom")&&(n.custom=s.google.api.CustomHttpPattern.toObject(e.custom,t),t.oneofs)&&(n.pattern="custom"),e.additional_bindings&&e.additional_bindings.length){n.additional_bindings=[];for(var r=0;r<e.additional_bindings.length;++r)n.additional_bindings[r]=s.google.api.HttpRule.toObject(e.additional_bindings[r],t)}return null!=e.response_body&&e.hasOwnProperty("response_body")&&(n.response_body=e.response_body),n},p.prototype.toJSON=function(){return this.constructor.toObject(this,e.util.toJSONOptions)},p),n.CustomHttpPattern=(u.prototype.kind="",u.prototype.path="",u.create=function(e){return new u(e)},u.encode=function(e,t){return t=t||o.create(),null!=e.kind&&Object.hasOwnProperty.call(e,"kind")&&t.uint32(10).string(e.kind),null!=e.path&&Object.hasOwnProperty.call(e,"path")&&t.uint32(18).string(e.path),t},u.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},u.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new s.google.api.CustomHttpPattern;e.pos<n;){var o=e.uint32();switch(o>>>3){case 1:r.kind=e.string();break;case 2:r.path=e.string();break;default:e.skipType(7&o)}}return r},u.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},u.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.kind&&e.hasOwnProperty("kind")&&!l.isString(e.kind)?"kind: string expected":null!=e.path&&e.hasOwnProperty("path")&&!l.isString(e.path)?"path: string expected":null},u.fromObject=function(e){var t;return e instanceof s.google.api.CustomHttpPattern?e:(t=new s.google.api.CustomHttpPattern,null!=e.kind&&(t.kind=String(e.kind)),null!=e.path&&(t.path=String(e.path)),t)},u.toObject=function(e,t){var n={};return(t=t||{}).defaults&&(n.kind="",n.path=""),null!=e.kind&&e.hasOwnProperty("kind")&&(n.kind=e.kind),null!=e.path&&e.hasOwnProperty("path")&&(n.path=e.path),n},u.prototype.toJSON=function(){return this.constructor.toObject(this,e.util.toJSONOptions)},u),n),r),s});