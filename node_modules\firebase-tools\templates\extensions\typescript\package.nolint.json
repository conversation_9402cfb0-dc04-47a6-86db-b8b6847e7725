{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "mocha": "mocha '**/*.spec.ts'", "test": "(cd integration-tests && firebase emulators:exec 'npm run mocha' -P demo-test)"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1"}, "devDependencies": {"@types/chai": "^4.3.4", "@types/mocha": "^10.0.1", "typescript": "^4.9.0", "axios": "^1.3.2", "chai": "^4.3.7", "mocha": "^10.2.0", "ts-node": "^10.4.0"}, "private": true}