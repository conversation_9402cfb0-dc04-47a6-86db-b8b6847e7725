// Exam Preview functionality

let currentExam = null;
let examQuestions = [];

document.addEventListener('DOMContentLoaded', function() {
    initializeExamPreview();
    checkAuthentication();
});

function initializeExamPreview() {
    setupUserMenu();
    setupEventListeners();
    loadExamData();
}

// Check if user is authenticated
function checkAuthentication() {
    const user = localStorage.getItem('user') || sessionStorage.getItem('user');
    
    if (!user) {
        window.location.href = 'auth.html?redirect=exam-preview.html';
        return;
    }
    
    const userData = JSON.parse(user);
    updateUserInfo(userData);
}

// Update user information in the UI
function updateUserInfo(userData) {
    const userName = document.getElementById('userName');
    const userAvatar = document.getElementById('userAvatar');
    
    if (userName) {
        userName.textContent = userData.name || userData.email.split('@')[0];
    }
    
    if (userAvatar && userData.name) {
        userAvatar.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.name)}&background=3b82f6&color=fff`;
    }
}

// Setup user menu
function setupUserMenu() {
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userDropdown = document.getElementById('userDropdown');
    const logoutBtn = document.getElementById('logoutBtn');
    
    userMenuBtn.addEventListener('click', function() {
        userDropdown.classList.toggle('hidden');
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!userMenuBtn.contains(e.target) && !userDropdown.contains(e.target)) {
            userDropdown.classList.add('hidden');
        }
    });
    
    logoutBtn.addEventListener('click', function(e) {
        e.preventDefault();
        logout();
    });
}

// Setup event listeners
function setupEventListeners() {
    // Main action buttons
    document.getElementById('saveExamBtn').addEventListener('click', saveExam);
    document.getElementById('startExamBtn').addEventListener('click', startExam);
    
    // Secondary action buttons
    document.getElementById('addQuestionBtn').addEventListener('click', showAddQuestionModal);
    document.getElementById('regenerateBtn').addEventListener('click', regenerateQuestions);
    document.getElementById('exportBtn').addEventListener('click', exportExam);
    
    // Modal controls
    document.getElementById('closeModalBtn').addEventListener('click', hideAddQuestionModal);
    document.getElementById('cancelAddBtn').addEventListener('click', hideAddQuestionModal);
    document.getElementById('addQuestionForm').addEventListener('submit', addNewQuestion);
    
    // Question type change
    document.getElementById('questionType').addEventListener('change', handleQuestionTypeChange);
}

// Load exam data
async function loadExamData() {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const examId = urlParams.get('id');
        
        if (!examId) {
            showErrorState();
            return;
        }
        
        // Try to load from Firestore first, then fallback to localStorage
        const exam = await loadExamFromDatabase(examId) || loadExamFromLocalStorage(examId);
        
        if (!exam) {
            showErrorState();
            return;
        }
        
        currentExam = exam;
        examQuestions = exam.questions || [];
        
        displayExamInfo(exam);
        displayQuestions(examQuestions);
        
        hideLoadingState();
        showExamContent();
        
    } catch (error) {
        console.error('Error loading exam:', error);
        showErrorState();
    }
}

// Load exam from database (Firestore)
async function loadExamFromDatabase(examId) {
    try {
        const { db } = await import('./firebase-config.js');
        const { doc, getDoc } = await import('firebase/firestore');
        
        const examDoc = await getDoc(doc(db, 'exams', examId));
        
        if (examDoc.exists()) {
            return { id: examDoc.id, ...examDoc.data() };
        }
        
        return null;
    } catch (error) {
        console.error('Error loading from Firestore:', error);
        return null;
    }
}

// Load exam from localStorage (fallback)
function loadExamFromLocalStorage(examId) {
    try {
        const userExams = JSON.parse(localStorage.getItem('userExams') || '[]');
        return userExams.find(exam => exam.id === examId);
    } catch (error) {
        console.error('Error loading from localStorage:', error);
        return null;
    }
}

// Display exam information
function displayExamInfo(exam) {
    document.getElementById('questionsCount').textContent = exam.questions?.length || 0;
    document.getElementById('examDuration').textContent = exam.config?.examDuration || '--';
    document.getElementById('difficultyLevel').textContent = getDifficultyText(exam.config?.difficulty);
    
    document.getElementById('examTitle').value = exam.title || '';
    document.getElementById('examDescription').value = exam.description || '';
}

// Get difficulty text in Arabic
function getDifficultyText(difficulty) {
    const difficultyMap = {
        'easy': 'سهل',
        'medium': 'متوسط',
        'hard': 'صعب',
        'mixed': 'مختلط'
    };
    return difficultyMap[difficulty] || 'متوسط';
}

// Display questions
function displayQuestions(questions) {
    const container = document.getElementById('questionsContainer');
    container.innerHTML = '';
    
    questions.forEach((question, index) => {
        const questionElement = createQuestionElement(question, index);
        container.appendChild(questionElement);
    });
}

// Create question element
function createQuestionElement(question, index) {
    const div = document.createElement('div');
    div.className = 'bg-white rounded-lg shadow-lg p-6';
    div.dataset.questionId = question.id;
    
    let optionsHtml = '';
    
    if (question.type === 'mcq') {
        optionsHtml = question.options.map((option, i) => `
            <div class="flex items-center mb-2">
                <span class="w-6 h-6 rounded-full border-2 ${i === question.correctAnswer ? 'bg-green-500 border-green-500' : 'border-gray-300'} flex items-center justify-center text-white text-sm mr-3">
                    ${String.fromCharCode(65 + i)}
                </span>
                <span class="${i === question.correctAnswer ? 'font-semibold text-green-700' : 'text-gray-700'}">${option}</span>
            </div>
        `).join('');
    } else if (question.type === 'trueFalse') {
        optionsHtml = `
            <div class="flex space-x-4 space-x-reverse">
                <span class="px-3 py-1 rounded-full text-sm ${question.correctAnswer ? 'bg-green-100 text-green-800 font-semibold' : 'bg-gray-100 text-gray-600'}">صح</span>
                <span class="px-3 py-1 rounded-full text-sm ${!question.correctAnswer ? 'bg-green-100 text-green-800 font-semibold' : 'bg-gray-100 text-gray-600'}">خطأ</span>
            </div>
        `;
    } else if (question.type === 'fillBlank') {
        optionsHtml = `
            <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                <span class="text-green-800 font-semibold">الإجابة: ${question.correctAnswer}</span>
            </div>
        `;
    }
    
    div.innerHTML = `
        <div class="flex justify-between items-start mb-4">
            <div class="flex items-center">
                <span class="bg-primary-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold mr-3">
                    ${index + 1}
                </span>
                <div>
                    <span class="text-xs text-gray-500 uppercase tracking-wide">${getQuestionTypeText(question.type)}</span>
                    <div class="flex items-center mt-1">
                        <span class="text-xs px-2 py-1 rounded-full ${getDifficultyClass(question.difficulty)}">${getDifficultyText(question.difficulty)}</span>
                    </div>
                </div>
            </div>
            <div class="flex space-x-2 space-x-reverse">
                <button onclick="editQuestion(${question.id})" class="text-blue-600 hover:text-blue-700 text-sm">
                    <i class="fas fa-edit mr-1"></i>
                    تعديل
                </button>
                <button onclick="deleteQuestion(${question.id})" class="text-red-600 hover:text-red-700 text-sm">
                    <i class="fas fa-trash mr-1"></i>
                    حذف
                </button>
            </div>
        </div>
        
        <div class="mb-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">${question.question}</h3>
            ${optionsHtml}
        </div>
        
        ${question.explanation ? `
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
                <div class="flex items-start">
                    <i class="fas fa-lightbulb text-blue-600 mt-1 mr-2"></i>
                    <div>
                        <h4 class="text-sm font-semibold text-blue-800 mb-1">شرح الإجابة</h4>
                        <p class="text-blue-700 text-sm">${question.explanation}</p>
                    </div>
                </div>
            </div>
        ` : ''}
    `;
    
    return div;
}

// Get question type text
function getQuestionTypeText(type) {
    const typeMap = {
        'mcq': 'اختيار من متعدد',
        'trueFalse': 'صح وخطأ',
        'fillBlank': 'املأ الفراغ'
    };
    return typeMap[type] || type;
}

// Get difficulty CSS class
function getDifficultyClass(difficulty) {
    const classMap = {
        'easy': 'bg-green-100 text-green-800',
        'medium': 'bg-yellow-100 text-yellow-800',
        'hard': 'bg-red-100 text-red-800',
        'mixed': 'bg-purple-100 text-purple-800'
    };
    return classMap[difficulty] || 'bg-gray-100 text-gray-800';
}

// Show/hide states
function hideLoadingState() {
    document.getElementById('loadingState').classList.add('hidden');
}

function showExamContent() {
    document.getElementById('examInfo').classList.remove('hidden');
    document.getElementById('questionsContainer').classList.remove('hidden');
    document.getElementById('actionButtons').classList.remove('hidden');
}

function showErrorState() {
    document.getElementById('loadingState').classList.add('hidden');
    document.getElementById('errorState').classList.remove('hidden');
}

// Save exam
async function saveExam() {
    try {
        const title = document.getElementById('examTitle').value.trim();
        const description = document.getElementById('examDescription').value.trim();
        
        if (!title) {
            showNotification('يرجى إدخال عنوان الامتحان', 'error');
            return;
        }
        
        currentExam.title = title;
        currentExam.description = description;
        currentExam.questions = examQuestions;
        currentExam.updatedAt = new Date().toISOString();
        
        // Save to database and localStorage
        await saveExamToDatabase(currentExam);
        saveExamToLocalStorage(currentExam);
        
        showNotification('تم حفظ الامتحان بنجاح', 'success');
        
    } catch (error) {
        console.error('Error saving exam:', error);
        showNotification('حدث خطأ أثناء حفظ الامتحان', 'error');
    }
}

// Save exam to database
async function saveExamToDatabase(exam) {
    try {
        const { db } = await import('./firebase-config.js');
        const { doc, setDoc } = await import('firebase/firestore');
        
        await setDoc(doc(db, 'exams', exam.id), exam);
    } catch (error) {
        console.error('Error saving to Firestore:', error);
        // Continue with localStorage save
    }
}

// Save exam to localStorage
function saveExamToLocalStorage(exam) {
    try {
        const userExams = JSON.parse(localStorage.getItem('userExams') || '[]');
        const existingIndex = userExams.findIndex(e => e.id === exam.id);
        
        if (existingIndex >= 0) {
            userExams[existingIndex] = exam;
        } else {
            userExams.push(exam);
        }
        
        localStorage.setItem('userExams', JSON.stringify(userExams));
    } catch (error) {
        console.error('Error saving to localStorage:', error);
    }
}

// Start exam
function startExam() {
    if (!currentExam || !examQuestions.length) {
        showNotification('لا توجد أسئلة في الامتحان', 'error');
        return;
    }
    
    // Save current state before starting
    saveExam().then(() => {
        window.location.href = `exam.html?id=${currentExam.id}`;
    });
}

// Show add question modal
function showAddQuestionModal() {
    document.getElementById('addQuestionModal').classList.remove('hidden');
    resetAddQuestionForm();
}

// Hide add question modal
function hideAddQuestionModal() {
    document.getElementById('addQuestionModal').classList.add('hidden');
}

// Reset add question form
function resetAddQuestionForm() {
    document.getElementById('addQuestionForm').reset();
    handleQuestionTypeChange(); // Reset visibility of options
}

// Handle question type change
function handleQuestionTypeChange() {
    const questionType = document.getElementById('questionType').value;
    const mcqOptions = document.getElementById('mcqOptions');
    const trueFalseOptions = document.getElementById('trueFalseOptions');
    const fillBlankOptions = document.getElementById('fillBlankOptions');

    // Hide all options first
    mcqOptions.classList.add('hidden');
    trueFalseOptions.classList.add('hidden');
    fillBlankOptions.classList.add('hidden');

    // Show relevant options
    if (questionType === 'mcq') {
        mcqOptions.classList.remove('hidden');
    } else if (questionType === 'trueFalse') {
        trueFalseOptions.classList.remove('hidden');
    } else if (questionType === 'fillBlank') {
        fillBlankOptions.classList.remove('hidden');
    }
}

// Add new question
function addNewQuestion(e) {
    e.preventDefault();

    const questionType = document.getElementById('questionType').value;
    const questionText = document.getElementById('questionText').value.trim();
    const questionExplanation = document.getElementById('questionExplanation').value.trim();
    const questionDifficulty = document.getElementById('questionDifficulty').value;

    if (!questionText) {
        showNotification('يرجى إدخال نص السؤال', 'error');
        return;
    }

    let correctAnswer;
    let options = [];

    if (questionType === 'mcq') {
        // Get MCQ options
        const optionInputs = document.querySelectorAll('[data-option]');
        options = Array.from(optionInputs).map(input => input.value.trim()).filter(option => option);

        if (options.length < 2) {
            showNotification('يرجى إدخال خيارين على الأقل', 'error');
            return;
        }

        const correctOptionRadio = document.querySelector('input[name="correctOption"]:checked');
        if (!correctOptionRadio) {
            showNotification('يرجى اختيار الإجابة الصحيحة', 'error');
            return;
        }

        correctAnswer = parseInt(correctOptionRadio.value);

    } else if (questionType === 'trueFalse') {
        const trueFalseRadio = document.querySelector('input[name="trueFalseAnswer"]:checked');
        if (!trueFalseRadio) {
            showNotification('يرجى اختيار الإجابة الصحيحة', 'error');
            return;
        }

        correctAnswer = trueFalseRadio.value === 'true';

    } else if (questionType === 'fillBlank') {
        correctAnswer = document.getElementById('fillBlankAnswer').value.trim();
        if (!correctAnswer) {
            showNotification('يرجى إدخال الإجابة الصحيحة', 'error');
            return;
        }
    }

    // Create new question object
    const newQuestion = {
        id: Date.now(), // Simple ID generation
        type: questionType,
        question: questionText,
        correctAnswer: correctAnswer,
        explanation: questionExplanation || null,
        difficulty: questionDifficulty
    };

    if (questionType === 'mcq') {
        newQuestion.options = options;
    }

    // Add to questions array
    examQuestions.push(newQuestion);

    // Update display
    displayQuestions(examQuestions);
    document.getElementById('questionsCount').textContent = examQuestions.length;

    // Hide modal and show success message
    hideAddQuestionModal();
    showNotification('تم إضافة السؤال بنجاح', 'success');
}

// Edit question
function editQuestion(questionId) {
    const question = examQuestions.find(q => q.id === questionId);
    if (!question) return;

    // Fill the form with question data
    document.getElementById('questionType').value = question.type;
    document.getElementById('questionText').value = question.question;
    document.getElementById('questionExplanation').value = question.explanation || '';
    document.getElementById('questionDifficulty').value = question.difficulty;

    if (question.type === 'mcq') {
        const optionInputs = document.querySelectorAll('[data-option]');
        question.options.forEach((option, index) => {
            if (optionInputs[index]) {
                optionInputs[index].value = option;
            }
        });

        const correctRadio = document.querySelector(`input[name="correctOption"][value="${question.correctAnswer}"]`);
        if (correctRadio) {
            correctRadio.checked = true;
        }

    } else if (question.type === 'trueFalse') {
        const trueFalseRadio = document.querySelector(`input[name="trueFalseAnswer"][value="${question.correctAnswer}"]`);
        if (trueFalseRadio) {
            trueFalseRadio.checked = true;
        }

    } else if (question.type === 'fillBlank') {
        document.getElementById('fillBlankAnswer').value = question.correctAnswer;
    }

    handleQuestionTypeChange();

    // Remove the old question and show modal
    deleteQuestion(questionId, false);
    showAddQuestionModal();
}

// Delete question
function deleteQuestion(questionId, showConfirm = true) {
    if (showConfirm && !confirm('هل أنت متأكد من حذف هذا السؤال؟')) {
        return;
    }

    examQuestions = examQuestions.filter(q => q.id !== questionId);
    displayQuestions(examQuestions);
    document.getElementById('questionsCount').textContent = examQuestions.length;

    if (showConfirm) {
        showNotification('تم حذف السؤال', 'info');
    }
}

// Regenerate questions
async function regenerateQuestions() {
    if (!confirm('هل أنت متأكد من إعادة توليد الأسئلة؟ سيتم فقدان التعديلات الحالية.')) {
        return;
    }

    try {
        showNotification('جاري إعادة توليد الأسئلة...', 'info');

        // Here you would call the AI service again
        // For now, we'll just shuffle existing questions
        examQuestions = [...examQuestions].sort(() => Math.random() - 0.5);

        displayQuestions(examQuestions);
        showNotification('تم إعادة توليد الأسئلة بنجاح', 'success');

    } catch (error) {
        console.error('Error regenerating questions:', error);
        showNotification('حدث خطأ أثناء إعادة توليد الأسئلة', 'error');
    }
}

// Export exam
function exportExam() {
    if (!currentExam || !examQuestions.length) {
        showNotification('لا توجد أسئلة للتصدير', 'error');
        return;
    }

    const examData = {
        title: currentExam.title,
        description: currentExam.description,
        questions: examQuestions,
        config: currentExam.config,
        createdAt: currentExam.createdAt,
        exportedAt: new Date().toISOString()
    };

    const dataStr = JSON.stringify(examData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `${currentExam.title || 'exam'}.json`;
    link.click();

    showNotification('تم تصدير الامتحان بنجاح', 'success');
}

// Logout function
function logout() {
    localStorage.removeItem('user');
    sessionStorage.removeItem('user');
    showNotification('تم تسجيل الخروج بنجاح', 'success');
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1000);
}
