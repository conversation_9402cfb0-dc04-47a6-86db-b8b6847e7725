// Authentication JavaScript for PDF to Exam Converter with Supabase
import { 
    supabase, 
    authHelpers, 
    utils 
} from './supabase-config.js';

document.addEventListener('DOMContentLoaded', function() {
    initializeAuth();
    checkExistingSession();
});

// Check if user is already logged in
async function checkExistingSession() {
    try {
        const user = await authHelpers.getCurrentUser();
        if (user) {
            // User is already logged in, redirect to dashboard
            window.location.href = 'dashboard.html';
        }
    } catch (error) {
        console.log('No existing session found');
    }
}

function initializeAuth() {
    // Setup tab navigation
    setupTabNavigation();
    
    // Setup form handlers
    setupFormHandlers();
    
    // Setup password toggles
    setupPasswordToggles();
    
    // Setup Google authentication
    setupGoogleAuth();
    
    // Check URL parameters for default tab
    checkURLParameters();
    
    // Check if user is already logged in
    checkAuthStatus();
}

// Check authentication status
async function checkAuthStatus() {
    try {
        const user = await authHelpers.getCurrentUser();
        if (user) {
            // User is already logged in, redirect to dashboard
            window.location.href = 'dashboard.html';
        }
    } catch (error) {
        console.error('Error checking auth status:', error);
    }
}

// Setup tab navigation
function setupTabNavigation() {
    const loginTab = document.getElementById('loginTab');
    const signupTab = document.getElementById('signupTab');
    
    if (loginTab) {
        loginTab.addEventListener('click', function() {
            showLoginForm();
        });
    }
    
    if (signupTab) {
        signupTab.addEventListener('click', function() {
            showSignupForm();
        });
    }
}

function showLoginForm() {
    const loginTab = document.getElementById('loginTab');
    const signupTab = document.getElementById('signupTab');
    const loginForm = document.getElementById('loginForm');
    const signupForm = document.getElementById('signupForm');
    
    if (loginTab && signupTab && loginForm && signupForm) {
        loginTab.classList.add('bg-primary-600', 'text-white');
        loginTab.classList.remove('text-gray-600');
        signupTab.classList.remove('bg-primary-600', 'text-white');
        signupTab.classList.add('text-gray-600');
        
        loginForm.classList.remove('hidden');
        signupForm.classList.add('hidden');
        
        // Update URL
        history.pushState(null, null, '?tab=login');
    }
}

function showSignupForm() {
    const loginTab = document.getElementById('loginTab');
    const signupTab = document.getElementById('signupTab');
    const loginForm = document.getElementById('loginForm');
    const signupForm = document.getElementById('signupForm');
    
    if (loginTab && signupTab && loginForm && signupForm) {
        signupTab.classList.add('bg-primary-600', 'text-white');
        signupTab.classList.remove('text-gray-600');
        loginTab.classList.remove('bg-primary-600', 'text-white');
        loginTab.classList.add('text-gray-600');
        
        signupForm.classList.remove('hidden');
        loginForm.classList.add('hidden');
        
        // Update URL
        history.pushState(null, null, '?tab=signup');
    }
}

// Setup form handlers
function setupFormHandlers() {
    const loginForm = document.getElementById('loginFormElement');
    const signupForm = document.getElementById('signupFormElement');
    const forgotPasswordLink = document.getElementById('forgotPassword');
    const resendConfirmationLink = document.getElementById('resendConfirmation');

    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    if (signupForm) {
        signupForm.addEventListener('submit', handleSignup);
    }

    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', handleForgotPassword);
    }

    if (resendConfirmationLink) {
        resendConfirmationLink.addEventListener('click', handleResendConfirmation);
    }
}

// Handle login
async function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    const submitBtn = e.target.querySelector('button[type="submit"]');
    
    // Validate inputs
    if (!email || !password) {
        utils.showNotification('يرجى ملء جميع الحقول', 'error');
        return;
    }
    
    if (!isValidEmail(email)) {
        utils.showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
        return;
    }
    
    // Show loading state
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'جاري تسجيل الدخول...';
    submitBtn.disabled = true;
    
    try {
        const { data, error } = await authHelpers.signIn(email, password);
        
        if (error) {
            throw error;
        }
        
        utils.showNotification('تم تسجيل الدخول بنجاح!', 'success');
        
        // Redirect to dashboard
        setTimeout(() => {
            window.location.href = 'dashboard.html';
        }, 1000);
        
    } catch (error) {
        console.error('Login error:', error);
        
        let errorMessage = 'حدث خطأ في تسجيل الدخول';

        if (error.message.includes('Invalid login credentials') || error.message.includes('invalid_credentials')) {
            errorMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        } else if (error.message.includes('Email not confirmed') || error.message.includes('email_not_confirmed')) {
            errorMessage = 'يرجى تأكيد البريد الإلكتروني أولاً';
        } else if (error.message.includes('Too many requests')) {
            errorMessage = 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً';
        } else if (error.message.includes('Network')) {
            errorMessage = 'مشكلة في الاتصال. يرجى التحقق من الإنترنت';
        }
        
        utils.showNotification(errorMessage, 'error');
    } finally {
        // Reset button state
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

// Handle signup
async function handleSignup(e) {
    e.preventDefault();
    
    const name = document.getElementById('signupName').value;
    const email = document.getElementById('signupEmail').value;
    const password = document.getElementById('signupPassword').value;
    const confirmPassword = document.getElementById('signupConfirmPassword').value;
    const submitBtn = e.target.querySelector('button[type="submit"]');
    
    // Validate inputs
    if (!name || !email || !password || !confirmPassword) {
        utils.showNotification('يرجى ملء جميع الحقول', 'error');
        return;
    }
    
    if (!isValidEmail(email)) {
        utils.showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
        return;
    }
    
    if (password.length < 6) {
        utils.showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        utils.showNotification('كلمة المرور وتأكيدها غير متطابقتين', 'error');
        return;
    }
    
    // Show loading state
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'جاري إنشاء الحساب...';
    submitBtn.disabled = true;
    
    try {
        const { data, error } = await authHelpers.signUp(email, password, {
            full_name: name
        });
        
        if (error) {
            throw error;
        }
        
        utils.showNotification('تم إنشاء الحساب بنجاح! يرجى تأكيد البريد الإلكتروني', 'success');
        
        // Switch to login form
        showLoginForm();
        
    } catch (error) {
        console.error('Signup error:', error);
        
        let errorMessage = 'حدث خطأ في إنشاء الحساب';

        if (error.message.includes('User already registered') || error.message.includes('already_registered')) {
            errorMessage = 'هذا البريد الإلكتروني مسجل مسبقاً';
        } else if (error.message.includes('Password should be at least 6 characters') || error.message.includes('password_too_short')) {
            errorMessage = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        } else if (error.message.includes('Invalid email')) {
            errorMessage = 'البريد الإلكتروني غير صحيح';
        } else if (error.message.includes('Signup is disabled')) {
            errorMessage = 'التسجيل معطل حالياً';
        }
        
        utils.showNotification(errorMessage, 'error');
    } finally {
        // Reset button state
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

// Setup Google authentication
function setupGoogleAuth() {
    const googleSignInBtn = document.getElementById('googleSignInBtn');

    if (googleSignInBtn) {
        googleSignInBtn.addEventListener('click', handleGoogleAuth);
    }
}

// Handle Google authentication
async function handleGoogleAuth(e) {
    e.preventDefault();
    
    const btn = e.target.closest('button');
    const originalText = btn.textContent;
    
    // Show loading state
    btn.textContent = 'جاري التوصيل مع Google...';
    btn.disabled = true;
    
    try {
        const { data, error } = await authHelpers.signInWithGoogle();
        
        if (error) {
            throw error;
        }
        
        utils.showNotification('تم تسجيل الدخول بنجاح!', 'success');
        
    } catch (error) {
        console.error('Google auth error:', error);
        utils.showNotification('حدث خطأ في تسجيل الدخول بـ Google', 'error');
        
        // Reset button state
        btn.textContent = originalText;
        btn.disabled = false;
    }
}

// Setup password toggles
function setupPasswordToggles() {
    const toggles = document.querySelectorAll('.password-toggle');
    
    toggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const input = this.previousElementSibling;
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
}

// Check URL parameters
function checkURLParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    
    if (tab === 'signup') {
        showSignupForm();
    } else {
        showLoginForm();
    }
}

// Utility function to validate email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Handle forgot password
async function handleForgotPassword(e) {
    e.preventDefault();

    const email = document.getElementById('loginEmail').value;

    if (!email) {
        utils.showNotification('يرجى إدخال البريد الإلكتروني أولاً', 'error');
        return;
    }

    if (!isValidEmail(email)) {
        utils.showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
        return;
    }

    try {
        const { data, error } = await authHelpers.resetPassword(email);

        if (error) {
            throw error;
        }

        utils.showNotification('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني', 'success');

    } catch (error) {
        console.error('Reset password error:', error);
        utils.showNotification('حدث خطأ في إرسال رابط إعادة تعيين كلمة المرور', 'error');
    }
}

// Handle resend confirmation
async function handleResendConfirmation(e) {
    e.preventDefault();

    const email = document.getElementById('loginEmail').value;

    if (!email) {
        utils.showNotification('يرجى إدخال البريد الإلكتروني أولاً', 'error');
        return;
    }

    if (!isValidEmail(email)) {
        utils.showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
        return;
    }

    try {
        const { data, error } = await authHelpers.resendConfirmation(email);

        if (error) {
            throw error;
        }

        utils.showNotification('تم إعادة إرسال رسالة التأكيد إلى بريدك الإلكتروني', 'success');

    } catch (error) {
        console.error('Resend confirmation error:', error);
        utils.showNotification('حدث خطأ في إعادة إرسال رسالة التأكيد', 'error');
    }
}
