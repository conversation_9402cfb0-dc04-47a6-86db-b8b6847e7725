{"version": 3, "file": "resolving-call.js", "sourceRoot": "", "sources": ["../../src/resolving-call.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,yDAAqD;AASrD,2CAA8D;AAC9D,yCAMoB;AAGpB,yCAAsC;AACtC,qCAAqC;AACrC,iEAAwE;AAExE,MAAM,WAAW,GAAG,gBAAgB,CAAC;AAErC,MAAa,aAAa;IA6BxB,YACmB,OAAwB,EACxB,MAAc,EAC/B,OAA0B,EACT,kBAAsC,EAC/C,UAAkB;QAJT,YAAO,GAAP,OAAO,CAAiB;QACxB,WAAM,GAAN,MAAM,CAAQ;QAEd,uBAAkB,GAAlB,kBAAkB,CAAoB;QAC/C,eAAU,GAAV,UAAU,CAAQ;QAjCpB,UAAK,GAAyC,IAAI,CAAC;QACnD,gBAAW,GAAG,KAAK,CAAC;QACpB,mBAAc,GACpB,IAAI,CAAC;QACC,qBAAgB,GAAG,KAAK,CAAC;QACzB,UAAK,GAAG,KAAK,CAAC;QACd,sBAAiB,GAAG,KAAK,CAAC;QAC1B,uBAAkB,GAAG,KAAK,CAAC;QAC3B,uBAAkB,GAAwB,IAAI,CAAC;QAC/C,aAAQ,GAAoB,IAAI,CAAC;QACjC,aAAQ,GAAgC,IAAI,CAAC;QAG7C,mBAAc,GAAuC,EAAE,CAAC;QACxD,kBAAa,GAAmB,UAAU,CAAC,GAAG,EAAE,GAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxD,gBAAW,GAAuB,IAAI,CAAC;QAEvC,sBAAiB,GAAgB,IAAI,CAAC;QACtC,uBAAkB,GAAgB,IAAI,CAAC;QACvC,mBAAc,GAAgB,IAAI,CAAC;QAE3C;;;;WAIG;QACK,gBAAW,GAAoB,kCAAe,CAAC,WAAW,EAAE,CAAC;QASnE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,IAAI,OAAO,CAAC,KAAK,GAAG,qBAAS,CAAC,YAAY,EAAE,CAAC;gBAC3C,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;oBACtC,IAAI,CAAC,gBAAgB,CAAC,kBAAM,CAAC,SAAS,EAAE,0BAA0B,CAAC,CAAC;gBACtE,CAAC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,OAAO,CAAC,KAAK,GAAG,qBAAS,CAAC,QAAQ,EAAE,CAAC;gBACvC,IAAI,CAAC,KAAK,CACR,oCAAoC;oBAClC,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,CACnC,CAAC;gBACF,IAAI,CAAC,QAAQ,GAAG,IAAA,sBAAW,EACzB,IAAI,CAAC,QAAQ,EACb,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,CACjC,CAAC;YACJ,CAAC;QACH,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,KAAK,CAAC,IAAY;QACxB,OAAO,CAAC,KAAK,CACX,wBAAY,CAAC,KAAK,EAClB,WAAW,EACX,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,IAAI,CACpC,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,IAAI,EAAE,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAA,2BAAgB,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3D,MAAM,OAAO,GAAG,IAAA,6BAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,8BAA8B,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC;YAC5D,MAAM,cAAc,GAAG,GAAG,EAAE;gBAC1B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC5B,IAAI,CAAC,gBAAgB,CAAC,kBAAM,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;oBACrE,OAAO;gBACT,CAAC;gBACD,MAAM,YAAY,GAAa,EAAE,CAAC;gBAClC,MAAM,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;gBACnC,YAAY,CAAC,IAAI,CAAC,2BAA2B,IAAA,+BAAoB,EAAC,IAAI,CAAC,iBAAiB,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC;gBAC9G,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5B,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACrD,YAAY,CAAC,IAAI,CAAC,oBAAoB,IAAA,+BAAoB,EAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;oBACjH,CAAC;oBACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBACxB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;4BAClD,YAAY,CAAC,IAAI,CAAC,qBAAqB,IAAA,+BAAoB,EAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;wBAC/G,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,YAAY,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;oBACpD,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,YAAY,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBACnD,CAAC;gBACD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBACf,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC;gBACrD,CAAC;gBACD,IAAI,CAAC,gBAAgB,CAAC,kBAAM,CAAC,iBAAiB,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1E,CAAC,CAAC;YACF,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC;gBACjB,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,MAAoB;QACvC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;YAC5D,CAAC;YACD,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACjC,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAChE,IAAI,CAAC,KAAK,CACR,0BAA0B;gBACxB,cAAc,CAAC,IAAI;gBACnB,YAAY;gBACZ,cAAc,CAAC,OAAO;gBACtB,GAAG,CACN,CAAC;YACF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;YAChE,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;;gBACpB,MAAA,IAAI,CAAC,QAAQ,0CAAE,eAAe,CAAC,cAAc,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,OAAuB,EAAE,OAAe;QACjE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,WAAY,CAAC,WAAW,CAC3B,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAC5D,CAAC,IAAI,CACJ,eAAe,CAAC,EAAE;YAChB,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAChC,KAAK,CAAC,sBAAsB,CAAC,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC;YAC/D,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,KAAK,CAAC,SAAS,EAAE,CAAC;YACpB,CAAC;QACH,CAAC,EACD,CAAC,MAAoB,EAAE,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACrD,CAAC,CACF,CAAC;IACJ,CAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxE,IAAI,YAAY,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACtC,OAAO;QACT,CAAC;aAAM,IAAI,YAAY,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,YAAY,EAAE,CAAC;gBAC5C,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACxC,CAAC;YACD,OAAO;QACT,CAAC;QACD,kCAAkC;QAClC,IAAI,CAAC,kBAAkB,GAAG,IAAI,IAAI,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;QACnC,IAAI,MAAM,CAAC,MAAM,KAAK,kBAAM,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,qDAA8B,EACtD,MAAM,CAAC,MAAM,EACb,iCAAiC,GAAG,IAAI,CAAC,MAAM,CAChD,CAAC;YACF,IAAI,CAAC,YAAY,CAAC;gBAChB,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE,IAAI,mBAAQ,EAAE;aACzB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;YAClC,cAAc,CAAC,UAAU,CACvB,cAAc,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAClE,CAAC;YACF,cAAc,CAAC,eAAe,CAC5B,cAAc,CAAC,eAAe,EAAE;gBAC9B,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,GAAG,OAAS,CAChD,CAAC;YACF,IAAI,CAAC,QAAQ,GAAG,IAAA,sBAAW,EAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAC3D,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;QAC1D,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAChE,gBAAgB,CAAC,EAAE;YACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAC1C,MAAM,EACN,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,QAAQ,CACd,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,GAAG,CAAC,CAAC;YACjE,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBACjC,iBAAiB,EAAE,QAAQ,CAAC,EAAE;oBAC5B,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;oBAChC,IAAI,CAAC,QAAS,CAAC,iBAAiB,CAC9B,IAAI,CAAC,WAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,CAC5C,CAAC;gBACJ,CAAC;gBACD,gBAAgB,EAAE,OAAO,CAAC,EAAE;oBAC1B,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;oBAC/B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAC9B,IAAI,CAAC,WAAY,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,CAC5C,gBAAgB,CAAC,EAAE;wBACjB,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;wBAClD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;wBAC/B,IAAI,CAAC,QAAS,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;wBAClD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;4BAC5B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAC7C,CAAC;oBACH,CAAC,EACD,CAAC,MAAoB,EAAE,EAAE;wBACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;oBACrD,CAAC,CACF,CAAC;gBACJ,CAAC;gBACD,eAAe,EAAE,MAAM,CAAC,EAAE;oBACxB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;oBAC9B,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAC3B,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;oBACnC,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;oBAC5B,CAAC;gBACH,CAAC;aACF,CAAC,CAAC;YACH,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACzB,CAAC;YACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,IAAI,CAAC,kBAAkB,CACrB,IAAI,CAAC,cAAc,CAAC,OAAO,EAC3B,IAAI,CAAC,cAAc,CAAC,OAAO,CAC5B,CAAC;YACJ,CAAC;iBAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACjC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACzB,CAAC;QACH,CAAC,EACD,CAAC,MAAoB,EAAE,EAAE;YACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC,CACF,CAAC;IACJ,CAAC;IAED,mBAAmB,CAAC,MAAoB;;QACtC,IAAI,MAAA,IAAI,CAAC,QAAQ,0CAAE,UAAU,GAAG,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IACD,gBAAgB,CAAC,MAAc,EAAE,OAAe;;QAC9C,IAAI,CAAC,KAAK,CACR,yBAAyB,GAAG,MAAM,GAAG,aAAa,GAAG,OAAO,GAAG,GAAG,CACnE,CAAC;QACF,MAAA,IAAI,CAAC,KAAK,0CAAE,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,YAAY,CAAC;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,IAAI,mBAAQ,EAAE;SACzB,CAAC,CAAC;IACL,CAAC;IACD,OAAO;;QACL,OAAO,MAAA,MAAA,IAAI,CAAC,KAAK,0CAAE,OAAO,EAAE,mCAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;IAC3D,CAAC;IACD,KAAK,CAAC,QAAkB,EAAE,QAA8B;QACtD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IACD,sBAAsB,CAAC,OAAuB,EAAE,OAAe;QAC7D,IAAI,CAAC,KAAK,CAAC,wCAAwC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QACtE,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;QAC7C,CAAC;IACH,CAAC;IACD,SAAS;QACP,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC/B,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,CAAC;IACH,CAAC;IACD,SAAS;QACP,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC/B,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC;IACH,CAAC;IACD,cAAc,CAAC,WAA4B;QACzC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,gBAAgB,CAAC,OAAuC;QACtD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;CACF;AAvUD,sCAuUC"}