{"name": "pdf-to-exam-converter", "version": "1.0.0", "description": "تطبيق ويب لتحويل ملفات PDF إلى امتحانات تفاعلية باستخدام Firebase و Gemini AI", "main": "index.js", "scripts": {"start": "firebase serve", "build": "npm run build:css", "build:css": "tailwindcss -i ./src/input.css -o ./public/css/style.css --watch", "deploy": "firebase deploy", "emulators": "firebase emulators:start"}, "keywords": ["pdf", "exam", "ai", "firebase", "gemini", "education"], "author": "PDF to <PERSON>am <PERSON>er", "license": "MIT", "dependencies": {"firebase": "^10.7.1", "pdf-lib": "^1.17.1", "jspdf": "^2.5.1"}, "devDependencies": {"tailwindcss": "^3.3.6", "firebase-tools": "^13.0.2"}}