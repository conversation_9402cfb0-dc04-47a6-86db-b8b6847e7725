// Upload functionality for PDF to Exam Converter
import {
    supabase,
    authHelpers,
    dbHelpers,
    storageHelpers,
    utils
} from './supabase-config.js';

let selectedFile = null;
let uploadProgress = 0;

document.addEventListener('DOMContentLoaded', function() {
    initializeUpload();
    checkAuthentication();
});

function initializeUpload() {
    setupFileUpload();
    setupConfigurationOptions();
    setupUserMenu();
    setupActionButtons();
}

// Check if user is authenticated
async function checkAuthentication() {
    try {
        const user = await authHelpers.getCurrentUser();

        if (!user) {
            window.location.href = 'auth.html?redirect=upload.html';
            return;
        }

        updateUserInfo(user);
    } catch (error) {
        console.error('Error checking authentication:', error);
        window.location.href = 'auth.html';
    }
}

// Update user information in the UI
function updateUserInfo(userData) {
    const userName = document.getElementById('userName');
    const userAvatar = document.getElementById('userAvatar');

    if (userName) {
        userName.textContent = userData.user_metadata?.full_name || userData.email.split('@')[0];
    }

    if (userAvatar) {
        const name = userData.user_metadata?.full_name || userData.email;
        userAvatar.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3b82f6&color=fff`;
    }
}

// Setup file upload functionality
function setupFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const selectFileBtn = document.getElementById('selectFileBtn');
    
    // Click to select file
    selectFileBtn.addEventListener('click', () => {
        fileInput.click();
    });
    
    // File input change
    fileInput.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            handleFileSelection(file);
        }
    });
    
    // Drag and drop functionality
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('drag-over');
    });
    
    uploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('drag-over');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        }
    });
    
    // File preview buttons
    const removeFileBtn = document.getElementById('removeFileBtn');
    const changeFileBtn = document.getElementById('changeFileBtn');
    
    removeFileBtn.addEventListener('click', removeFile);
    changeFileBtn.addEventListener('click', () => {
        fileInput.click();
    });
}

// Handle file selection
function handleFileSelection(file) {
    // Validate file using utils
    const validation = utils.validateFile(file);
    if (!validation.valid) {
        utils.showNotification(validation.error, 'error');
        return;
    }

    selectedFile = file;
    showFilePreview(file);
    showConfigurationOptions();
    enableGenerateButton();
}

// Show file preview
function showFilePreview(file) {
    const uploadContent = document.getElementById('uploadContent');
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    
    uploadContent.classList.add('hidden');
    filePreview.classList.remove('hidden');
    
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
}

// Remove selected file
function removeFile() {
    selectedFile = null;
    
    const uploadContent = document.getElementById('uploadContent');
    const filePreview = document.getElementById('filePreview');
    const configOptions = document.getElementById('configOptions');
    const fileInput = document.getElementById('fileInput');
    
    uploadContent.classList.remove('hidden');
    filePreview.classList.add('hidden');
    configOptions.classList.add('hidden');
    
    fileInput.value = '';
    disableGenerateButton();
}

// Show configuration options
function showConfigurationOptions() {
    const configOptions = document.getElementById('configOptions');
    configOptions.classList.remove('hidden');
}

// Setup configuration options
function setupConfigurationOptions() {
    const timeLimitCheckbox = document.getElementById('timeLimit');
    const timeLimitInput = document.getElementById('timeLimitInput');
    
    timeLimitCheckbox.addEventListener('change', function() {
        if (this.checked) {
            timeLimitInput.classList.remove('hidden');
        } else {
            timeLimitInput.classList.add('hidden');
        }
    });
}

// Setup user menu
function setupUserMenu() {
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userDropdown = document.getElementById('userDropdown');
    const logoutBtn = document.getElementById('logoutBtn');
    
    userMenuBtn.addEventListener('click', function() {
        userDropdown.classList.toggle('hidden');
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!userMenuBtn.contains(e.target) && !userDropdown.contains(e.target)) {
            userDropdown.classList.add('hidden');
        }
    });
    
    logoutBtn.addEventListener('click', function(e) {
        e.preventDefault();
        logout();
    });
}

// Setup action buttons
function setupActionButtons() {
    const generateExamBtn = document.getElementById('generateExamBtn');
    const resetBtn = document.getElementById('resetBtn');
    
    generateExamBtn.addEventListener('click', generateExam);
    resetBtn.addEventListener('click', resetForm);
}

// Enable generate button
function enableGenerateButton() {
    const generateExamBtn = document.getElementById('generateExamBtn');
    generateExamBtn.disabled = false;
}

// Disable generate button
function disableGenerateButton() {
    const generateExamBtn = document.getElementById('generateExamBtn');
    generateExamBtn.disabled = true;
}

// Generate exam
async function generateExam() {
    if (!selectedFile) {
        utils.showNotification('يرجى اختيار ملف PDF أولاً', 'error');
        return;
    }
    
    // Get configuration options
    const config = getConfigurationOptions();
    
    // Validate configuration
    if (!validateConfiguration(config)) {
        return;
    }
    
    // Show progress modal
    showProgressModal();
    
    try {
        // Step 1: Upload file
        updateProgress(1, 'رفع الملف...');
        const fileData = await uploadFileToStorage(selectedFile);

        // Step 2: Analyze content
        updateProgress(2, 'تحليل المحتوى...');
        await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate processing

        // Step 3: Generate questions
        updateProgress(3, 'توليد الأسئلة...');
        const examData = await generateQuestionsWithAI(fileData, config);

        // Step 4: Save to database
        updateProgress(4, 'حفظ الامتحان...');
        const examId = await saveExamToDatabase(examData);

        hideProgressModal();
        utils.showNotification('تم إنشاء الامتحان بنجاح!', 'success');

        // Redirect to exam preview
        setTimeout(() => {
            window.location.href = `exam-preview.html?id=${examId}`;
        }, 1000);

    } catch (error) {
        hideProgressModal();
        utils.showNotification('حدث خطأ أثناء إنشاء الامتحان: ' + error.message, 'error');
        console.error('Error generating exam:', error);
    }
}

// Get configuration options
function getConfigurationOptions() {
    return {
        language: document.getElementById('contentLanguage').value,
        questionTypes: {
            mcq: document.getElementById('mcqQuestions').checked,
            trueFalse: document.getElementById('trueFalseQuestions').checked,
            fillBlank: document.getElementById('fillBlankQuestions').checked
        },
        questionCount: parseInt(document.getElementById('questionCount').value),
        difficulty: document.getElementById('difficultyLevel').value,
        includeExplanations: document.getElementById('includeExplanations').checked,
        randomizeQuestions: document.getElementById('randomizeQuestions').checked,
        timeLimit: document.getElementById('timeLimit').checked,
        examDuration: document.getElementById('timeLimit').checked ?
            parseInt(document.getElementById('examDuration').value) : null
    };
}

// Validate configuration
function validateConfiguration(config) {
    // Check if at least one question type is selected
    const hasQuestionType = Object.values(config.questionTypes).some(type => type);
    if (!hasQuestionType) {
        utils.showNotification('يرجى اختيار نوع واحد على الأقل من الأسئلة', 'error');
        return false;
    }

    // Validate question count
    if (config.questionCount < 5 || config.questionCount > 50) {
        utils.showNotification('عدد الأسئلة يجب أن يكون بين 5 و 50', 'error');
        return false;
    }

    // Validate exam duration if time limit is enabled
    if (config.timeLimit && (config.examDuration < 5 || config.examDuration > 180)) {
        utils.showNotification('مدة الامتحان يجب أن تكون بين 5 و 180 دقيقة', 'error');
        return false;
    }

    return true;
}

// Upload file to Supabase Storage
async function uploadFileToStorage(file) {
    try {
        const user = await authHelpers.getCurrentUser();
        if (!user) {
            throw new Error('يجب تسجيل الدخول أولاً');
        }

        // Create unique filename
        const timestamp = Date.now();
        const fileName = `${user.id}/${timestamp}_${file.name}`;

        // Upload file to Supabase Storage
        const { data, error } = await storageHelpers.uploadPDF(fileName, file);

        if (error) {
            throw new Error(error.message);
        }

        // Get public URL
        const { data: urlData } = await storageHelpers.getPublicURL(fileName);

        return {
            filePath: fileName,
            publicUrl: urlData.publicUrl,
            fileName: file.name,
            fileSize: file.size
        };
    } catch (error) {
        console.error('Error uploading file:', error);
        throw new Error('فشل في رفع الملف: ' + error.message);
    }
}

// Generate questions using AI
async function generateQuestionsWithAI(fileData, config) {
    try {
        // For now, we'll use local generation with sample questions
        // In the future, this will integrate with Gemini AI API
        console.log('Generating questions with AI for file:', fileData.fileName);

        // Simulate AI processing time
        await new Promise(resolve => setTimeout(resolve, 2000));

        const examData = {
            title: selectedFile.name.replace('.pdf', ''),
            description: 'امتحان تم إنشاؤه تلقائياً من ملف PDF',
            config: config,
            questions: generateSampleQuestions(config),
            createdAt: new Date().toISOString(),
            fileUrl: fileData.publicUrl,
            filePath: fileData.filePath,
            fileName: fileData.fileName,
            fileSize: fileData.fileSize
        };

        return examData;

    } catch (error) {
        console.error('Error generating questions:', error);
        throw new Error('فشل في توليد الأسئلة: ' + error.message);
    }
}

// Generate sample questions (replace with actual AI-generated questions)
function generateSampleQuestions(config) {
    const questions = [];
    let questionId = 1;

    // Generate MCQ questions
    if (config.questionTypes.mcq) {
        const mcqCount = Math.floor(config.questionCount * 0.6);
        for (let i = 0; i < mcqCount; i++) {
            questions.push({
                id: questionId++,
                type: 'mcq',
                question: `سؤال اختيار من متعدد رقم ${i + 1}`,
                options: [
                    'الخيار الأول',
                    'الخيار الثاني',
                    'الخيار الثالث',
                    'الخيار الرابع'
                ],
                correctAnswer: 0,
                explanation: config.includeExplanations ? 'شرح الإجابة الصحيحة' : null,
                difficulty: config.difficulty === 'mixed' ?
                    ['easy', 'medium', 'hard'][Math.floor(Math.random() * 3)] : config.difficulty
            });
        }
    }

    // Generate True/False questions
    if (config.questionTypes.trueFalse) {
        const tfCount = Math.floor(config.questionCount * 0.3);
        for (let i = 0; i < tfCount; i++) {
            questions.push({
                id: questionId++,
                type: 'trueFalse',
                question: `سؤال صح وخطأ رقم ${i + 1}`,
                correctAnswer: Math.random() > 0.5,
                explanation: config.includeExplanations ? 'شرح الإجابة الصحيحة' : null,
                difficulty: config.difficulty === 'mixed' ?
                    ['easy', 'medium', 'hard'][Math.floor(Math.random() * 3)] : config.difficulty
            });
        }
    }

    // Generate Fill in the blank questions
    if (config.questionTypes.fillBlank) {
        const fillCount = config.questionCount - questions.length;
        for (let i = 0; i < fillCount; i++) {
            questions.push({
                id: questionId++,
                type: 'fillBlank',
                question: `أكمل الجملة التالية: هذا سؤال املأ الفراغ رقم ${i + 1} ______`,
                correctAnswer: 'الإجابة الصحيحة',
                explanation: config.includeExplanations ? 'شرح الإجابة الصحيحة' : null,
                difficulty: config.difficulty === 'mixed' ?
                    ['easy', 'medium', 'hard'][Math.floor(Math.random() * 3)] : config.difficulty
            });
        }
    }

    // Randomize questions if requested
    if (config.randomizeQuestions) {
        questions.sort(() => Math.random() - 0.5);
    }

    return questions.slice(0, config.questionCount);
}

// Save exam to database
async function saveExamToDatabase(examData) {
    try {
        const user = await authHelpers.getCurrentUser();
        if (!user) {
            throw new Error('يجب تسجيل الدخول أولاً');
        }

        // Generate unique exam ID
        const examId = utils.generateId();

        // Prepare exam record for database
        const examRecord = {
            id: examId,
            user_id: user.id,
            title: examData.title,
            description: examData.description,
            file_url: examData.fileUrl,
            file_path: examData.filePath,
            file_name: examData.fileName,
            file_size: examData.fileSize,
            questions: examData.questions,
            config: examData.config,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            status: 'draft'
        };

        // Save to Supabase database
        const { data, error } = await dbHelpers.createExam(examRecord);

        if (error) {
            throw new Error(error.message);
        }

        console.log('Exam saved successfully:', data);
        return examId;

    } catch (error) {
        console.error('Error saving exam to database:', error);
        throw new Error('فشل في حفظ الامتحان: ' + error.message);
    }
}

// Reset form
function resetForm() {
    removeFile();

    // Reset configuration options
    document.getElementById('contentLanguage').value = 'ar';
    document.getElementById('mcqQuestions').checked = true;
    document.getElementById('trueFalseQuestions').checked = true;
    document.getElementById('fillBlankQuestions').checked = false;
    document.getElementById('questionCount').value = 20;
    document.getElementById('difficultyLevel').value = 'medium';
    document.getElementById('includeExplanations').checked = true;
    document.getElementById('randomizeQuestions').checked = false;
    document.getElementById('timeLimit').checked = false;
    document.getElementById('examDuration').value = 60;

    // Hide time limit input
    document.getElementById('timeLimitInput').classList.add('hidden');

    utils.showNotification('تم إعادة تعيين النموذج', 'info');
}

// Show progress modal
function showProgressModal() {
    const modal = document.getElementById('progressModal');
    modal.classList.remove('hidden');
    uploadProgress = 0;
    updateProgressBar(0);
}

// Hide progress modal
function hideProgressModal() {
    const modal = document.getElementById('progressModal');
    modal.classList.add('hidden');
}

// Update progress
function updateProgress(step, message) {
    const progressText = document.getElementById('progressText');
    const steps = ['step1', 'step2', 'step3', 'step4'];

    progressText.textContent = message;

    // Update step indicators
    steps.forEach((stepId, index) => {
        const stepElement = document.getElementById(stepId);
        const icon = stepElement.querySelector('i');

        if (index < step - 1) {
            // Completed step
            icon.className = 'fas fa-check-circle mr-2 text-green-500';
            stepElement.classList.remove('text-gray-300');
            stepElement.classList.add('text-green-500');
        } else if (index === step - 1) {
            // Current step
            icon.className = 'fas fa-spinner fa-spin mr-2 text-blue-500';
            stepElement.classList.remove('text-gray-300');
            stepElement.classList.add('text-blue-500');
        } else {
            // Future step
            icon.className = 'fas fa-circle mr-2';
            stepElement.classList.add('text-gray-300');
        }
    });

    // Update progress bar
    const progress = (step / 4) * 100;
    updateProgressBar(progress);
}

// Update progress bar
function updateProgressBar(percentage) {
    const progressFill = document.getElementById('progressFill');
    progressFill.style.width = percentage + '%';
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Logout function
async function logout() {
    try {
        await authHelpers.signOut();
        utils.showNotification('تم تسجيل الخروج بنجاح', 'success');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1000);
    } catch (error) {
        console.error('Error during logout:', error);
        utils.showNotification('حدث خطأ أثناء تسجيل الخروج', 'error');
    }
}
