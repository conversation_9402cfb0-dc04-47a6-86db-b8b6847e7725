<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة الامتحان - محول PDF إلى امتحانات</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Supabase -->
    <script type="module" src="js/supabase-config.js"></script>
</head>
<body class="font-arabic bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-primary-600">
                            <i class="fas fa-file-pdf mr-2"></i>
                            محول PDF
                        </h1>
                    </a>
                </div>
                
                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-4 space-x-reverse">
                    <a href="dashboard.html" class="text-gray-500 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-tachometer-alt mr-1"></i>
                        لوحة التحكم
                    </a>
                    <a href="upload.html" class="text-gray-500 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-upload mr-1"></i>
                        رفع ملف جديد
                    </a>
                </div>
                
                <!-- User Menu -->
                <div class="flex items-center">
                    <div class="relative">
                        <button id="userMenuBtn" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <img id="userAvatar" class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=User&background=3b82f6&color=fff" alt="User">
                            <span id="userName" class="mr-2 text-gray-700">مستخدم</span>
                            <i class="fas fa-chevron-down mr-1 text-gray-400"></i>
                        </button>
                        
                        <!-- Dropdown menu -->
                        <div id="userDropdown" class="hidden absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                            <div class="py-1">
                                <a href="dashboard.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-tachometer-alt mr-2"></i>
                                    لوحة التحكم
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i>
                                    الملف الشخصي
                                </a>
                                <div class="border-t border-gray-100"></div>
                                <a href="#" id="logoutBtn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>
                                    تسجيل الخروج
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">
                        <i class="fas fa-eye text-primary-600 mr-2"></i>
                        معاينة الامتحان
                    </h1>
                    <p class="text-gray-600">راجع الأسئلة المولدة وقم بتعديلها حسب الحاجة</p>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <button id="saveExamBtn" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>
                        حفظ الامتحان
                    </button>
                    <button id="startExamBtn" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                        <i class="fas fa-play mr-2"></i>
                        بدء الامتحان
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loadingState" class="text-center py-12">
            <div class="loading-spinner mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">جاري تحميل الامتحان...</h3>
            <p class="text-gray-600">يرجى الانتظار</p>
        </div>

        <!-- Exam Info -->
        <div id="examInfo" class="hidden bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-primary-600" id="questionsCount">0</div>
                    <div class="text-sm text-gray-500">عدد الأسئلة</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600" id="examDuration">--</div>
                    <div class="text-sm text-gray-500">مدة الامتحان (دقيقة)</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600" id="difficultyLevel">متوسط</div>
                    <div class="text-sm text-gray-500">مستوى الصعوبة</div>
                </div>
            </div>
            
            <!-- Exam Title and Description -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="mb-4">
                    <label for="examTitle" class="block text-sm font-medium text-gray-700 mb-2">عنوان الامتحان</label>
                    <input type="text" id="examTitle" class="input-field" placeholder="أدخل عنوان الامتحان">
                </div>
                <div>
                    <label for="examDescription" class="block text-sm font-medium text-gray-700 mb-2">وصف الامتحان</label>
                    <textarea id="examDescription" rows="3" class="input-field" placeholder="أدخل وصف الامتحان"></textarea>
                </div>
            </div>
        </div>

        <!-- Questions Container -->
        <div id="questionsContainer" class="hidden space-y-6">
            <!-- Questions will be dynamically loaded here -->
        </div>

        <!-- Error State -->
        <div id="errorState" class="hidden text-center py-12">
            <div class="text-red-500 text-6xl mb-4">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">حدث خطأ في تحميل الامتحان</h3>
            <p class="text-gray-600 mb-4">لم نتمكن من العثور على الامتحان المطلوب</p>
            <a href="upload.html" class="btn-primary">
                <i class="fas fa-upload mr-2"></i>
                إنشاء امتحان جديد
            </a>
        </div>

        <!-- Action Buttons -->
        <div id="actionButtons" class="hidden flex justify-center space-x-4 space-x-reverse mt-8">
            <button id="addQuestionBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>
                إضافة سؤال
            </button>
            <button id="regenerateBtn" class="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200">
                <i class="fas fa-sync-alt mr-2"></i>
                إعادة توليد
            </button>
            <button id="exportBtn" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200">
                <i class="fas fa-download mr-2"></i>
                تصدير
            </button>
        </div>
    </main>

    <!-- Add Question Modal -->
    <div id="addQuestionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-900">إضافة سؤال جديد</h3>
                <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form id="addQuestionForm">
                <div class="mb-4">
                    <label for="questionType" class="block text-sm font-medium text-gray-700 mb-2">نوع السؤال</label>
                    <select id="questionType" class="input-field">
                        <option value="mcq">اختيار من متعدد</option>
                        <option value="trueFalse">صح وخطأ</option>
                        <option value="fillBlank">املأ الفراغ</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="questionText" class="block text-sm font-medium text-gray-700 mb-2">نص السؤال</label>
                    <textarea id="questionText" rows="3" class="input-field" placeholder="أدخل نص السؤال" required></textarea>
                </div>
                
                <!-- MCQ Options -->
                <div id="mcqOptions" class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الخيارات</label>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="radio" name="correctOption" value="0" class="mr-2">
                            <input type="text" class="input-field flex-1" placeholder="الخيار الأول" data-option="0">
                        </div>
                        <div class="flex items-center">
                            <input type="radio" name="correctOption" value="1" class="mr-2">
                            <input type="text" class="input-field flex-1" placeholder="الخيار الثاني" data-option="1">
                        </div>
                        <div class="flex items-center">
                            <input type="radio" name="correctOption" value="2" class="mr-2">
                            <input type="text" class="input-field flex-1" placeholder="الخيار الثالث" data-option="2">
                        </div>
                        <div class="flex items-center">
                            <input type="radio" name="correctOption" value="3" class="mr-2">
                            <input type="text" class="input-field flex-1" placeholder="الخيار الرابع" data-option="3">
                        </div>
                    </div>
                </div>
                
                <!-- True/False Options -->
                <div id="trueFalseOptions" class="mb-4 hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الإجابة الصحيحة</label>
                    <div class="flex space-x-4 space-x-reverse">
                        <label class="flex items-center">
                            <input type="radio" name="trueFalseAnswer" value="true" class="mr-2">
                            <span>صح</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="trueFalseAnswer" value="false" class="mr-2">
                            <span>خطأ</span>
                        </label>
                    </div>
                </div>
                
                <!-- Fill Blank Answer -->
                <div id="fillBlankOptions" class="mb-4 hidden">
                    <label for="fillBlankAnswer" class="block text-sm font-medium text-gray-700 mb-2">الإجابة الصحيحة</label>
                    <input type="text" id="fillBlankAnswer" class="input-field" placeholder="أدخل الإجابة الصحيحة">
                </div>
                
                <div class="mb-4">
                    <label for="questionExplanation" class="block text-sm font-medium text-gray-700 mb-2">شرح الإجابة (اختياري)</label>
                    <textarea id="questionExplanation" rows="2" class="input-field" placeholder="أدخل شرح الإجابة الصحيحة"></textarea>
                </div>
                
                <div class="mb-6">
                    <label for="questionDifficulty" class="block text-sm font-medium text-gray-700 mb-2">مستوى الصعوبة</label>
                    <select id="questionDifficulty" class="input-field">
                        <option value="easy">سهل</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="hard">صعب</option>
                    </select>
                </div>
                
                <div class="flex justify-end space-x-2 space-x-reverse">
                    <button type="button" id="cancelAddBtn" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة السؤال
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="js/supabase-config.js"></script>
    <script type="module" src="js/exam-preview.js"></script>
</body>
</html>
