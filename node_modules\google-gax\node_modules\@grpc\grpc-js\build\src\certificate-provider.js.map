{"version": 3, "file": "certificate-provider.js", "sourceRoot": "", "sources": ["../../src/certificate-provider.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,yBAAyB;AACzB,qCAAqC;AACrC,2CAA2C;AAC3C,+BAAiC;AAEjC,MAAM,WAAW,GAAG,sBAAsB,CAAC;AAE3C,SAAS,KAAK,CAAC,IAAY;IACzB,OAAO,CAAC,KAAK,CAAC,wBAAY,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACvD,CAAC;AAiCD,MAAM,eAAe,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAE/C,MAAa,8BAA8B;IASzC,YACU,MAA4C;QAA5C,WAAM,GAAN,MAAM,CAAsC;QAT9C,iBAAY,GAA0B,IAAI,CAAC;QAC3C,sBAAiB,GAA+G,IAAI,CAAC;QACrI,mBAAc,GAA2C,SAAS,CAAC;QACnE,gBAAW,GAAqC,IAAI,GAAG,EAAE,CAAC;QAC1D,yBAAoB,GAAiD,SAAS,CAAC;QAC/E,sBAAiB,GAA2C,IAAI,GAAG,EAAE,CAAC;QACtE,mBAAc,GAAgB,IAAI,CAAC;QAKzC,IAAI,CAAC,MAAM,CAAC,eAAe,KAAK,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,KAAK,SAAS,CAAC,EAAE,CAAC;YACrF,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;QACtF,CAAC;QACD,IAAI,MAAM,CAAC,eAAe,KAAK,SAAS,IAAI,MAAM,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;QACvF,CAAC;QACD,KAAK,CAAC,uCAAuC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IAC1E,CAAC;IAEO,kBAAkB;QACxB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QACD,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,UAAU,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAU;YACrG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAU;YACnG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAU;SAC1G,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,mBAAmB,CAAC,EAAE,EAAE;YACzF,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvB,OAAO;YACT,CAAC;YACD,KAAK,CAAC,6CAA6C,GAAG,iBAAiB,CAAC,MAAM,GAAG,eAAe,GAAG,gBAAgB,CAAC,MAAM,GAAG,mBAAmB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC/K,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,IAAI,iBAAiB,CAAC,MAAM,KAAK,WAAW,IAAI,gBAAgB,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBACxF,IAAI,CAAC,oBAAoB,GAAG;oBAC1B,WAAW,EAAE,iBAAiB,CAAC,KAAK;oBACpC,UAAU,EAAE,gBAAgB,CAAC,KAAK;iBACnC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACnC,CAAC;YACD,IAAI,mBAAmB,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBAC/C,IAAI,CAAC,cAAc,GAAG;oBACpB,aAAa,EAAE,mBAAmB,CAAC,KAAK;iBACzC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC7B,CAAC;YACD,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC9C,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACtC,CAAC;YACD,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACxC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAChC,CAAC;QACH,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,2CAA2C,CAAC,CAAC;IACrD,CAAC;IAEO,uBAAuB;QAC7B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB;;;kCAGsB;YACtB,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;YACpH,IAAI,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBACxD,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,CAAC;YACD,IAAI,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;gBAC5D,qDAAqD;gBACrD,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;gBAChC,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;YACxC,CAAC;YACD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAChG,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAEO,sBAAsB;QAC5B,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACrE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAC3B,CAAC;QACH,CAAC;IACH,CAAC;IAED,wBAAwB,CAAC,QAAqC;QAC5D,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IACD,2BAA2B,CAAC,QAAqC;QAC/D,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IACD,8BAA8B,CAAC,QAA2C;QACxE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YAC5C,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IACD,iCAAiC,CAAC,QAA2C;QAC3E,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;CACF;AAlHD,wEAkHC"}