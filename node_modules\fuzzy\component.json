{"name": "fuzzy", "description": "small, standalone fuzzy search / fuzzy filter. browser or node", "version": "0.1.0", "homepage": "https://github.com/myork/fuzzy", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "mattyork.org"}, "repository": {"type": "git", "url": "git://github.com/myork/fuzzy.git"}, "bugs": {"url": "https://github.com/myork/fuzzy/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/myork/fuzzy/blob/master/LICENSE-MIT"}], "main": "lib/fuzzy.js", "scripts": ["lib/fuzzy.js"], "keywords": ["fuzzy", "search", "filter"]}